#!/usr/bin/env python3
"""
测试系统集成流程
"""

from perfect_unified_tcm_system import IntelligentResponseGenerator
import logging

logging.basicConfig(level=logging.INFO)

def test_integration():
    print("🧪 测试系统集成流程...")
    
    # 初始化响应生成器
    print("📦 初始化响应生成器...")
    generator = IntelligentResponseGenerator()

    # 初始化系统
    print("🚀 初始化系统...")
    success = generator.initialize()
    print(f"✅ 初始化结果: {success}")

    if not success:
        print("❌ 系统初始化失败")
        return
    
    # 测试查询
    test_queries = [
        "肾虚怎么治疗？",
        "栀子甘草豉汤方的组成是什么？",
        "失眠多梦怎么办？"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: {query}")
        print("=" * 50)
        
        try:
            # 生成回答
            response = generator.generate_response(query)
            
            print(f"📝 回答长度: {len(response)} 字符")
            print(f"📄 回答预览: {response[:200]}...")
            
            # 检查回答质量
            if len(response) > 50:
                print("✅ 回答生成成功")
            else:
                print("⚠️ 回答过短，可能存在问题")
                
        except Exception as e:
            print(f"❌ 生成回答失败: {e}")
    
    print("\n🎯 集成测试完成")

if __name__ == "__main__":
    test_integration()

{"timestamp": "2025-06-23T09:30:18.581591", "cleanup_summary": {"deleted_files": 2, "deleted_directories": 2}, "deleted_files": ["fast_startup_test.py", "final_cleanup_report.json"], "deleted_directories": ["vector_db", "__pycache__"], "final_structure": {"core_files": ["perfect_unified_tcm_system.py", "intelligent_rag_retriever.py", "intelligent_mcp_service.py", "deepseek_ollama_api.py", "gemma3_ollama_api.py", "intelligent_model_selector.py", "fast_launcher.py", "perfect_launcher.py", "config.py", "requirements_perfect.txt", "README_PERFECT.md"], "test_files": ["test_vector_db.py", "test_mcp_service.py", "test_system_integration.py"], "directories": ["documents", "perfect_vector_db", "conversations", "uploads", "models", "logs", "cache"]}, "file_count": {"core_files": 11, "test_files": 3, "total_files": 14, "directories": 7}}
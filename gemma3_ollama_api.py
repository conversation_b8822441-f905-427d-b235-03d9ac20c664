#!/usr/bin/env python3
"""
Gemma 3 4B-QAT Ollama API接口
优化版本，专注于性能和中医专业回答
"""

import requests
import json
import logging
from typing import Optional, Dict, Any
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Gemma3OllamaAPI:
    """Gemma 3 4B-QAT Ollama API接口"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model_name = "gemma3:4b-it-qat"  # Gemma 3 4B IT-QAT量化版本
        self.available = False
        self.session = requests.Session()
        
        # 优化连接设置
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive'
        })
        
        # 检查模型可用性
        self.check_model_availability()
    
    def check_model_availability(self) -> bool:
        """检查Gemma 3模型是否可用"""
        try:
            # 检查Ollama服务
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                logger.warning("⚠️ Ollama服务不可用")
                return False
            
            # 检查模型是否已下载
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            if self.model_name in model_names:
                self.available = True
                logger.info(f"✅ Gemma 3模型 {self.model_name} 可用")
                return True
            else:
                logger.warning(f"⚠️ Gemma 3模型 {self.model_name} 未找到")
                logger.info("💡 可用模型列表:")
                for model_name in model_names:
                    logger.info(f"   - {model_name}")
                
                # 尝试自动下载模型
                if self.auto_download_model():
                    self.available = True
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"❌ 检查Gemma 3模型失败: {e}")
            return False
    
    def auto_download_model(self) -> bool:
        """自动下载Gemma 3模型"""
        logger.info(f"🔄 正在下载Gemma 3模型 {self.model_name}...")
        
        try:
            pull_data = {"name": self.model_name}
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json=pull_data,
                timeout=300,  # 5分钟超时
                stream=True
            )
            
            if response.status_code == 200:
                # 处理流式响应
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if 'status' in data:
                                logger.info(f"📥 {data['status']}")
                            if data.get('status') == 'success':
                                logger.info(f"✅ Gemma 3模型下载完成")
                                return True
                        except json.JSONDecodeError:
                            continue
            
            logger.error("❌ Gemma 3模型下载失败")
            return False
            
        except Exception as e:
            logger.error(f"❌ 下载Gemma 3模型异常: {e}")
            return False
    
    def generate_response(self, 
                         query: str, 
                         context: str = "", 
                         max_tokens: int = 200,
                         temperature: float = 0.3) -> Optional[str]:
        """生成中医专业回答"""
        if not self.available:
            logger.error("❌ Gemma 3模型不可用")
            return "❌ Gemma 3模型不可用，请检查Ollama服务状态"
        
        try:
            # 构建中医专业提示词
            system_prompt = """你是一位专业的中医智能助手，具备深厚的中医理论知识和丰富的临床经验。
请基于中医理论，为用户提供专业、准确、实用的中医建议。

回答要求：
1. 基于中医理论（阴阳五行、脏腑经络、气血津液等）
2. 提供具体的治疗方法（方剂、穴位、食疗等）
3. 回答简洁明了，重点突出
4. 如有相关文献资料，请结合使用
5. 强调个体差异，建议咨询专业中医师

请回答以下问题："""
            
            # 构建完整提示词
            if context:
                full_prompt = f"{system_prompt}\n\n参考资料：\n{context}\n\n用户问题：{query}"
            else:
                full_prompt = f"{system_prompt}\n\n用户问题：{query}"
            
            # 准备请求数据
            request_data = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens,
                    "top_k": 40,
                    "top_p": 0.9,
                    "repeat_penalty": 1.1,
                    "num_thread": 4  # 限制线程数
                }
            }
            
            # 发送请求
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=request_data,
                timeout=60  # 60秒超时
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                
                if generated_text:
                    response_time = end_time - start_time
                    logger.info(f"✅ Gemma 3生成回答成功，长度: {len(generated_text)}, 用时: {response_time:.2f}秒")
                    
                    # 添加中医专业格式化
                    formatted_response = self._format_tcm_response(generated_text)
                    return formatted_response
                else:
                    logger.warning("⚠️ Gemma 3生成空回答")
                    return "抱歉，未能生成有效回答，请重新提问。"
            else:
                logger.error(f"❌ Gemma 3请求失败: {response.status_code}")
                return f"❌ Gemma 3模型请求失败: {response.status_code}"
                
        except requests.exceptions.Timeout:
            logger.error("❌ Gemma 3请求超时")
            return "❌ 请求超时，请稍后重试"
        except Exception as e:
            logger.error(f"❌ Gemma 3生成回答异常: {e}")
            return f"❌ 生成回答时发生错误: {str(e)}"
    
    def _format_tcm_response(self, response: str) -> str:
        """格式化中医专业回答"""
        # 添加中医专业标识
        formatted = f"## 🏥 中医专业建议\n\n{response}"
        
        # 添加免责声明
        disclaimer = """

### ⚠️ 重要提醒
本回答仅供参考，不能替代专业医疗建议。具体诊疗请咨询专业中医师。

如需更详细的解答，请提供具体的症状描述或问题。"""
        
        return formatted + disclaimer
    
    def test_performance(self) -> Dict[str, Any]:
        """测试Gemma 3模型性能"""
        if not self.available:
            return {"error": "模型不可用"}
        
        test_queries = [
            "中医基础理论",
            "失眠的中医治疗",
            "脾胃虚弱调理"
        ]
        
        results = []
        total_time = 0
        
        for query in test_queries:
            start_time = time.time()
            response = self.generate_response(query, max_tokens=100)
            end_time = time.time()
            
            response_time = end_time - start_time
            total_time += response_time
            
            results.append({
                "query": query,
                "response_time": response_time,
                "response_length": len(response) if response else 0,
                "success": bool(response and "❌" not in response)
            })
        
        avg_time = total_time / len(test_queries)
        
        return {
            "model": self.model_name,
            "average_response_time": avg_time,
            "total_queries": len(test_queries),
            "results": results,
            "performance_rating": (
                "优秀" if avg_time < 5 else
                "良好" if avg_time < 10 else
                "一般" if avg_time < 20 else
                "需要优化"
            )
        }

# 测试函数
def test_gemma3_api():
    """测试Gemma 3 API"""
    print("🧪 测试Gemma 3 4B-QAT模型...")
    
    api = Gemma3OllamaAPI()
    
    if not api.available:
        print("❌ Gemma 3模型不可用")
        return False
    
    # 性能测试
    performance = api.test_performance()
    print(f"📊 性能测试结果:")
    print(f"   平均响应时间: {performance['average_response_time']:.2f}秒")
    print(f"   性能评级: {performance['performance_rating']}")
    
    # 功能测试
    test_query = "肾虚怎么治疗"
    print(f"\n🔍 功能测试: {test_query}")
    response = api.generate_response(test_query)
    print(f"📝 回答: {response[:200]}...")
    
    return True

if __name__ == "__main__":
    test_gemma3_api()

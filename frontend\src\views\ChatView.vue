<template>
  <div class="chat-view">
    <div class="row h-100">
      <!-- 聊天主界面 -->
      <div class="col-12">
        <div class="card h-100">
          <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <i class="bi bi-robot me-2"></i>
                <h5 class="mb-0">🏥 智能医生咨询</h5>
              </div>
              <div class="d-flex align-items-center">
                <!-- 语音开关 -->
                <div class="form-check form-switch me-3">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="voiceToggle"
                    v-model="voiceEnabled"
                  >
                  <label class="form-check-label text-white" for="voiceToggle">
                    <i class="bi bi-volume-up"></i>
                  </label>
                </div>
                
                <!-- 新建对话 -->
                <button 
                  class="btn btn-outline-light btn-sm"
                  @click="startNewChat"
                  :disabled="isLoading"
                >
                  <i class="bi bi-plus-circle me-1"></i>新对话
                </button>
              </div>
            </div>
          </div>

          <div class="card-body p-0 d-flex flex-column">
            <!-- 聊天消息区域 -->
            <div 
              ref="chatContainer"
              class="chat-container flex-grow-1 p-3"
              @scroll="handleScroll"
            >
              <!-- 欢迎消息 -->
              <div v-if="messages.length === 0" class="text-center text-muted py-5">
                <i class="bi bi-chat-heart display-1 mb-3"></i>
                <h4>欢迎使用家庭私人医生小帮手</h4>
                <p class="mb-4">我是您的专业中医智能助手，可以为您提供：</p>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <div class="card border-success">
                      <div class="card-body text-start">
                        <h6 class="card-title text-success">
                          <i class="bi bi-search me-2"></i>症状咨询
                        </h6>
                        <p class="card-text small">根据症状提供专业的中医分析和建议</p>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <div class="card border-success">
                      <div class="card-body text-start">
                        <h6 class="card-title text-success">
                          <i class="bi bi-book me-2"></i>方剂查询
                        </h6>
                        <p class="card-text small">查询经典方剂的组成、功效和应用</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-4">
                  <p class="text-muted small">
                    💡 试试问我："肾虚怎么治疗？" 或 "栀子甘草豉汤方的组成是什么？"
                  </p>
                </div>
              </div>

              <!-- 消息列表 -->
              <div 
                v-for="(message, index) in messages" 
                :key="index"
                :class="getMessageClass(message.role)"
              >
                <div v-if="message.role === 'user'" class="d-flex justify-content-end mb-3">
                  <div class="message-user">
                    <div class="mb-1">{{ message.content }}</div>
                    <small class="opacity-75">{{ formatTime(message.timestamp) }}</small>
                  </div>
                </div>

                <div v-else-if="message.role === 'assistant'" class="d-flex justify-content-start mb-3">
                  <div class="message-assistant">
                    <div class="d-flex align-items-start">
                      <i class="bi bi-robot text-success me-2 mt-1"></i>
                      <div class="flex-grow-1">
                        <div class="mb-2" v-html="formatMessage(message.content)"></div>
                        <div class="d-flex justify-content-between align-items-center">
                          <small class="text-muted">{{ formatTime(message.timestamp) }}</small>
                          <div class="btn-group btn-group-sm">
                            <button 
                              v-if="voiceEnabled"
                              class="btn btn-outline-success btn-sm"
                              @click="speakMessage(message.content)"
                              :disabled="isSpeaking"
                              title="朗读回答"
                            >
                              <i class="bi bi-volume-up"></i>
                            </button>
                            <button 
                              class="btn btn-outline-secondary btn-sm"
                              @click="copyMessage(message.content)"
                              title="复制回答"
                            >
                              <i class="bi bi-clipboard"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else-if="message.role === 'system'" class="message-system">
                  <i class="bi bi-info-circle me-1"></i>
                  {{ message.content }}
                </div>
              </div>

              <!-- 加载指示器 -->
              <div v-if="isLoading" class="typing-indicator">
                <i class="bi bi-robot text-success me-2"></i>
                <span>AI医生正在思考</span>
                <div class="typing-dots">
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                  <div class="typing-dot"></div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="card-footer bg-light">
              <div class="row g-2">
                <div class="col">
                  <div class="input-group">
                    <textarea
                      ref="messageInput"
                      v-model="currentMessage"
                      class="form-control"
                      placeholder="请输入您的健康问题..."
                      rows="2"
                      @keydown="handleKeyDown"
                      @input="handleInput"
                      :disabled="isLoading"
                      style="resize: none;"
                    ></textarea>
                    
                    <!-- 语音输入按钮 -->
                    <button 
                      v-if="speechRecognitionSupported"
                      :class="['btn', isRecording ? 'btn-danger' : 'btn-outline-success', 'voice-btn']"
                      @click="toggleVoiceInput"
                      :disabled="isLoading"
                      :title="isRecording ? '停止录音' : '语音输入'"
                    >
                      <i :class="isRecording ? 'bi-mic-fill' : 'bi-mic'"></i>
                    </button>
                  </div>
                </div>
                
                <div class="col-auto">
                  <button 
                    class="btn btn-success"
                    @click="sendMessage"
                    :disabled="!canSend"
                  >
                    <i class="bi bi-send-fill me-1"></i>
                    <span class="d-none d-sm-inline">发送</span>
                  </button>
                </div>
              </div>
              
              <!-- 输入提示 -->
              <div class="mt-2">
                <small class="text-muted">
                  <i class="bi bi-lightbulb me-1"></i>
                  按 Ctrl+Enter 快速发送 | 
                  <span v-if="speechRecognitionSupported">点击麦克风进行语音输入 | </span>
                  支持中医症状咨询和方剂查询
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 语音识别状态模态框 -->
    <div 
      v-if="isRecording" 
      class="modal fade show d-block" 
      style="background: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-body text-center py-4">
            <div class="mb-3">
              <i class="bi bi-mic-fill text-danger display-1 recording"></i>
            </div>
            <h5>正在监听您的语音...</h5>
            <p class="text-muted">请清晰地说出您的问题</p>
            <div class="mt-3">
              <button class="btn btn-outline-secondary" @click="stopVoiceInput">
                <i class="bi bi-stop-fill me-1"></i>停止录音
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import apiService from '../services/api'
import VoiceService from '../services/voice'

export default {
  name: 'ChatView',
  props: {
    sessionId: String
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const messages = ref([])
    const currentMessage = ref('')
    const isLoading = ref(false)
    const currentSessionId = ref(props.sessionId || null)
    const voiceEnabled = ref(true)
    const isSpeaking = ref(false)
    const isRecording = ref(false)
    const chatContainer = ref(null)
    const messageInput = ref(null)
    
    // 语音服务
    const voiceService = new VoiceService()
    
    // 计算属性
    const canSend = computed(() => {
      return currentMessage.value.trim().length > 0 && !isLoading.value
    })
    
    const speechRecognitionSupported = computed(() => {
      return voiceService.isRecognitionSupported()
    })

    // 方法
    const formatTime = (timestamp) => {
      return window.formatTime(timestamp)
    }

    const formatMessage = (content) => {
      // 简单的Markdown格式化
      return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
    }

    const getMessageClass = (role) => {
      return `message-${role}`
    }

    const scrollToBottom = () => {
      nextTick(() => {
        if (chatContainer.value) {
          chatContainer.value.scrollTop = chatContainer.value.scrollHeight
        }
      })
    }

    const sendMessage = async () => {
      if (!canSend.value) return

      const message = currentMessage.value.trim()
      currentMessage.value = ''
      isLoading.value = true

      try {
        // 添加用户消息
        messages.value.push({
          role: 'user',
          content: message,
          timestamp: new Date().toISOString()
        })
        
        scrollToBottom()

        // 发送到API
        const response = await apiService.sendMessage(message, currentSessionId.value)
        
        // 更新会话ID
        if (response.data.session_id) {
          currentSessionId.value = response.data.session_id
        }

        // 添加AI回答
        messages.value.push({
          role: 'assistant',
          content: response.data.response,
          timestamp: response.data.timestamp,
          metadata: {
            response_time: response.data.response_time
          }
        })

        // 自动语音播放
        if (voiceEnabled.value && response.data.response) {
          speakMessage(response.data.response)
        }

        scrollToBottom()

      } catch (error) {
        console.error('发送消息失败:', error)
        
        // 添加错误消息
        messages.value.push({
          role: 'system',
          content: `发送失败: ${error.message}`,
          timestamp: new Date().toISOString()
        })
        
        window.showNotification('error', `发送消息失败: ${error.message}`)
      } finally {
        isLoading.value = false
        scrollToBottom()
      }
    }

    const speakMessage = async (text) => {
      if (isSpeaking.value) return
      
      try {
        isSpeaking.value = true
        await voiceService.speak(text)
      } catch (error) {
        console.error('语音播放失败:', error)
        window.showNotification('error', '语音播放失败')
      } finally {
        isSpeaking.value = false
      }
    }

    const copyMessage = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        window.showNotification('success', '内容已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        window.showNotification('error', '复制失败')
      }
    }

    const toggleVoiceInput = () => {
      if (isRecording.value) {
        stopVoiceInput()
      } else {
        startVoiceInput()
      }
    }

    const startVoiceInput = async () => {
      try {
        isRecording.value = true
        const result = await voiceService.recognize()
        
        if (result && result.trim()) {
          currentMessage.value = result
          window.showNotification('success', '语音识别成功')
          
          // 自动发送（可选）
          // await sendMessage()
        } else {
          window.showNotification('warning', '未识别到语音内容')
        }
      } catch (error) {
        console.error('语音识别失败:', error)
        window.showNotification('error', `语音识别失败: ${error.message}`)
      } finally {
        isRecording.value = false
      }
    }

    const stopVoiceInput = () => {
      isRecording.value = false
      voiceService.stopRecognition()
    }

    const startNewChat = () => {
      messages.value = []
      currentSessionId.value = null
      currentMessage.value = ''
      
      // 更新URL
      if (route.params.sessionId) {
        router.push('/')
      }
      
      window.showNotification('info', '已开始新对话')
    }

    const loadSession = async (sessionId) => {
      if (!sessionId) return

      try {
        window.setGlobalLoading(true, '正在加载对话历史...')
        
        const response = await apiService.getSession(sessionId)
        const sessionData = response.data.session_data
        
        if (sessionData && sessionData.messages) {
          messages.value = sessionData.messages
          currentSessionId.value = sessionId
          scrollToBottom()
        }
      } catch (error) {
        console.error('加载会话失败:', error)
        window.showNotification('error', `加载对话失败: ${error.message}`)
      } finally {
        window.setGlobalLoading(false)
      }
    }

    const handleKeyDown = (event) => {
      if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault()
        sendMessage()
      }
    }

    const handleInput = () => {
      // 自动调整文本框高度
      const textarea = messageInput.value
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      }
    }

    const handleScroll = () => {
      // 可以在这里添加滚动相关的逻辑，比如加载更多历史消息
    }

    // 监听路由参数变化
    watch(() => route.params.sessionId, (newSessionId) => {
      if (newSessionId && newSessionId !== currentSessionId.value) {
        loadSession(newSessionId)
      }
    })

    // 生命周期
    onMounted(() => {
      // 如果有会话ID，加载对应的会话
      if (props.sessionId || route.params.sessionId) {
        loadSession(props.sessionId || route.params.sessionId)
      }
      
      // 聚焦输入框
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    })

    onUnmounted(() => {
      // 清理语音相关资源
      if (isRecording.value) {
        stopVoiceInput()
      }
      if (isSpeaking.value) {
        voiceService.stop()
      }
    })

    return {
      messages,
      currentMessage,
      isLoading,
      voiceEnabled,
      isSpeaking,
      isRecording,
      chatContainer,
      messageInput,
      canSend,
      speechRecognitionSupported,
      formatTime,
      formatMessage,
      getMessageClass,
      sendMessage,
      speakMessage,
      copyMessage,
      toggleVoiceInput,
      startVoiceInput,
      stopVoiceInput,
      startNewChat,
      handleKeyDown,
      handleInput,
      handleScroll
    }
  }
}
</script>

<style scoped>
.chat-view {
  height: calc(100vh - 200px);
  min-height: 600px;
}

.chat-container {
  max-height: 60vh;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.message-user,
.message-assistant,
.message-system {
  max-width: 80%;
  word-wrap: break-word;
}

@media (max-width: 768px) {
  .chat-view {
    height: calc(100vh - 150px);
  }
  
  .message-user,
  .message-assistant {
    max-width: 90%;
  }
  
  .card-header h5 {
    font-size: 1rem;
  }
}

.voice-btn.recording {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}
</style>

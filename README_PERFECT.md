# 🏥 完美统一中医智能助手

## 🎯 项目简介

这是一个集成了所有最佳功能的完美统一版本，删除了所有冗余文件，只保留这一个完美的中医智能助手系统。

## ✨ 核心功能

### 🎤 语音对话功能
- ✅ 语音输入识别
- ✅ 语音播放回答
- ✅ 中文语音支持
- ✅ 实时语音交互

### 🔍 智能检索系统
- ✅ RAG向量检索
- ✅ Elasticsearch全文检索
- ✅ 智能MCP服务集成
- ✅ 多策略融合检索

### 💬 聊天管理
- ✅ 自动保存对话历史
- ✅ 多会话管理
- ✅ 对话加载和切换
- ✅ 会话删除功能

### 📁 文档上传管理
- ✅ 支持PDF、Word、Excel、PPT、TXT
- ✅ 智能文档解析
- ✅ 文档块化处理
- ✅ 上传历史管理

### 🌐 多端访问
- ✅ Web界面访问
- ✅ 本地部署
- ✅ 移动端适配
- ✅ 跨平台支持

## 🚀 快速启动

### 1. 安装依赖

```bash
# 必需依赖
pip install streamlit requests

# 语音功能 (可选)
pip install pyttsx3 SpeechRecognition

# 文档处理 (可选)
pip install PyPDF2 python-docx pandas openpyxl python-pptx

# 向量搜索 (可选)
pip install faiss-cpu sentence-transformers
```

### 2. 启动系统

```bash
# 方法1: 使用完美启动脚本 (推荐)
python perfect_startup.py

# 方法2: 直接启动主程序
streamlit run perfect_unified_tcm_system.py
```

### 3. 访问系统

打开浏览器访问: http://localhost:8501

## 📋 文件结构

```
RAG 2025/
├── perfect_unified_tcm_system.py    # 🏥 主程序 (唯一完美版本)
├── perfect_startup.py               # 🚀 完美启动脚本
├── intelligent_mcp_service.py       # 🔍 智能MCP服务 (可选)
├── intelligent_rag_retriever.py     # 🧠 智能检索器 (可选)
├── README_PERFECT.md                # 📖 本文档
├── documents/                       # 📁 文档目录
├── conversations/                   # 💬 对话历史
├── uploads/                         # 📤 上传文件
└── perfect_vector_db/              # 🗄️ 向量数据库
```

## 🎯 使用指南

### 基本对话
1. 在输入框中输入问题
2. 点击"🚀 提交问题"
3. 查看智能回答

### 语音功能
1. 在侧边栏启用"语音功能"
2. 点击"🎤 语音输入"进行语音提问
3. 点击"🔊 朗读回答"听取回答

### 文档上传
1. 在侧边栏选择文档文件
2. 点击"📤 处理文档"
3. 系统自动解析并索引文档

### 对话管理
1. 点击"🆕 新对话"开始新会话
2. 在"📚 历史对话"中选择历史会话
3. 点击"📂 加载对话"切换会话

## 🧪 测试建议

### 基础测试
- "失眠多梦怎么办？"
- "肚子疼湿气重如何治疗？"
- "什么是阴阳学说？"
- "中医四诊是什么？"

### 功能测试
1. **语音测试**: 启用语音功能，尝试语音输入
2. **文档测试**: 上传PDF文档，测试解析效果
3. **对话测试**: 创建多个会话，测试切换功能
4. **检索测试**: 输入专业问题，查看检索效果

## 🔧 系统配置

### 配置文件位置
所有配置都在 `perfect_unified_tcm_system.py` 的 `CONFIG` 字典中。

### 主要配置项
```python
CONFIG = {
    'CHUNK_SIZE': 1000,              # 文档块大小
    'TOP_K': 10,                     # 检索结果数量
    'MIN_RELEVANCE_SCORE': 0.3,      # 最小相关度阈值
    'MAX_FILE_SIZE': 500 * 1024 * 1024,  # 最大文件大小
    'MCP_SERVICE_URL': 'http://localhost:8006',  # MCP服务地址
}
```

## 🛠️ 故障排除

### 常见问题

1. **语音功能不可用**
   - 安装语音依赖: `pip install pyttsx3 SpeechRecognition`
   - 检查麦克风权限

2. **文档上传失败**
   - 安装文档处理依赖: `pip install PyPDF2 python-docx`
   - 检查文件格式和大小

3. **MCP服务连接失败**
   - 检查 `intelligent_mcp_service.py` 是否存在
   - 确保端口8006未被占用

4. **向量搜索不可用**
   - 安装向量搜索依赖: `pip install faiss-cpu sentence-transformers`

### 日志查看
系统日志会显示在控制台和Streamlit界面中，注意查看错误信息。

## 📞 技术支持

如遇到问题，请检查：
1. Python版本 (建议3.8+)
2. 依赖包安装情况
3. 端口占用情况
4. 文件权限设置

## 🎉 版本特色

这个完美统一版本的特点：
- ✅ **唯一版本**: 删除所有冗余文件，只保留这一个完美版本
- ✅ **功能完整**: 集成所有最佳功能
- ✅ **代码优化**: 精简高效的代码结构
- ✅ **易于使用**: 一键启动，开箱即用
- ✅ **稳定可靠**: 经过充分测试和优化

---

🏥 **完美统一中医智能助手** - 您的专业中医AI伙伴！

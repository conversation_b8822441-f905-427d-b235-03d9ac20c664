# 🏥 家庭私人医生小帮手 v2.0.0

基于Vue.js + FastAPI + RAG + MCP架构的智能中医咨询系统，为家庭提供专业的中医健康咨询服务。

## ✨ 核心特性

### 🧠 智能医疗咨询
- **专业中医知识库**: 集成经典中医文献和现代医学资料
- **症状智能分析**: 基于用户描述提供专业的中医诊断建议
- **方剂精准查询**: 快速检索中药方剂的组成、功效和应用
- **个性化建议**: 根据体质和症状提供针对性的调理方案

### 🎤 多模态交互
- **语音对话**: 支持语音输入和语音播放，解放双手
- **实时流式响应**: SSE技术实现打字机效果，提升用户体验
- **移动端优化**: 响应式设计，完美适配手机和平板
- **直观界面**: 现代化UI设计，操作简单易懂

### 📚 文档管理
- **多格式支持**: PDF、TXT、DOCX等格式文档上传
- **智能解析**: 自动提取文档内容并建立向量索引
- **快速检索**: 基于语义相似度的智能文档搜索
- **版本管理**: 文档更新和历史版本追踪

### 💬 会话管理
- **历史记录**: 完整保存所有对话历史
- **会话恢复**: 随时继续之前的咨询对话
- **数据导出**: 支持对话记录导出和备份
- **隐私保护**: 本地存储，数据安全可控

### 🌐 远程访问
- **Ngrok隧道**: 一键开启远程访问功能
- **移动友好**: 支持手机远程访问
- **安全认证**: 密码保护，确保访问安全
- **24/7可用**: 支持全天候远程咨询服务

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue.js 前端   │    │  FastAPI 后端   │    │   MCP 服务      │
│                 │    │                 │    │                 │
│ • 用户界面      │◄──►│ • RESTful API   │◄──►│ • 智能检索      │
│ • 语音交互      │    │ • 会话管理      │    │ • 在线知识库    │
│ • 文档上传      │    │ • 文档处理      │    │ • 语义搜索      │
│ • 实时对话      │    │ • 向量检索      │    │ • 结果评分      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   DeepSeek AI   │
                    │                 │
                    │ • 智能问答      │
                    │ • 医学推理      │
                    │ • 知识整合      │
                    │ • 个性化回答    │
                    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **Node.js**: 16+
- **npm**: 8+
- **内存**: 8GB+ 推荐
- **存储**: 2GB+ 可用空间

### 一键启动

1. **克隆项目**
```bash
git clone <repository-url>
cd tcm-rag-system
```

2. **安装依赖**
```bash
# Python依赖
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
cd ..
```

3. **启动系统**
```bash
python perfect_startup.py
```

4. **访问系统**
- 前端界面: http://localhost:3000
- 后端API: http://localhost:5001
- MCP服务: http://localhost:8006

### 远程访问设置

1. **安装ngrok**
```bash
# 访问 https://ngrok.com/download 下载安装
# 注册账户并设置authtoken
ngrok authtoken YOUR_TOKEN
```

2. **启动远程访问**
```bash
python start_with_ngrok.py
```

3. **获取公网地址**
系统会自动生成公网访问地址，密码为: `MVP168918`

## 📖 使用指南

### 基础咨询

1. **症状咨询**
   - 描述症状: "我最近总是感觉疲劳，腰膝酸软"
   - 获得分析: 系统会分析可能的证型和调理建议

2. **方剂查询**
   - 询问方剂: "栀子甘草豉汤方的组成是什么？"
   - 获得详情: 方剂组成、功效、主治等完整信息

3. **体质调理**
   - 体质咨询: "肾虚怎么调理？"
   - 个性化方案: 获得针对性的调理建议

### 高级功能

1. **文档上传**
   - 支持格式: PDF、TXT、DOCX
   - 自动处理: 系统自动解析并建立索引
   - 智能检索: 上传后可直接询问文档内容

2. **语音交互**
   - 语音输入: 点击麦克风图标开始语音输入
   - 语音播放: 开启语音开关，AI回答会自动播放

3. **会话管理**
   - 历史查看: 在"对话历史"页面查看所有会话
   - 会话恢复: 点击历史会话可继续对话
   - 数据导出: 支持单个或批量导出会话记录

## 🔧 配置说明

### 系统配置

主要配置文件位于各服务的配置部分：

- **FastAPI后端**: `fastapi_backend.py` 中的 `CONFIG` 字典
- **MCP服务**: `intelligent_mcp_service.py` 中的配置
- **前端**: `frontend/src/main.js` 中的全局配置

### 关键配置项

```python
CONFIG = {
    'DOCUMENTS_PATH': 'documents',      # 文档存储路径
    'UPLOAD_PATH': 'uploads',           # 上传文件路径
    'VECTOR_DB_PATH': 'vector_db',      # 向量数据库路径
    'CONVERSATION_PATH': 'conversations', # 会话记录路径
    'MAX_FILE_SIZE': 200 * 1024 * 1024, # 最大文件大小
    'DEEPSEEK_API_KEY': 'your-api-key', # DeepSeek API密钥
    'EMBEDDING_MODEL': 'm3e-base'       # 嵌入模型
}
```

### 模型配置

系统使用以下模型：
- **嵌入模型**: m3e-base (本地部署)
- **语言模型**: DeepSeek-R1 (API调用)
- **相似度阈值**: 0.65

## 📁 项目结构

```
tcm-rag-system/
├── frontend/                   # Vue.js前端
│   ├── src/
│   │   ├── components/        # 组件
│   │   ├── views/            # 页面
│   │   ├── services/         # API服务
│   │   └── assets/           # 静态资源
│   ├── package.json
│   └── vue.config.js
├── documents/                 # 系统文档
├── uploads/                   # 用户上传文件
├── conversations/             # 会话记录
├── vector_db/                # 向量数据库
├── fastapi_backend.py        # FastAPI后端服务
├── intelligent_mcp_service.py # MCP智能服务
├── perfect_startup.py        # 系统启动脚本
├── start_with_ngrok.py       # Ngrok启动脚本
├── ngrok_tunnel.py           # Ngrok管理器
├── requirements.txt          # Python依赖
└── TCM_README.md             # 项目文档
```

## 🛠️ 技术栈

### 前端技术
- **Vue.js 3.x**: 现代化前端框架
- **Bootstrap 5.x**: 响应式UI组件库
- **Axios**: HTTP客户端
- **Web Speech API**: 语音识别和合成

### 后端技术
- **Flask**: 轻量级Web框架
- **FastMCP**: MCP服务框架
- **FAISS**: 向量相似度搜索
- **sentence-transformers**: 文本嵌入
- **DeepSeek API**: 大语言模型

### 数据处理
- **PyPDF2**: PDF文档解析
- **python-docx**: Word文档处理
- **jieba**: 中文分词
- **BeautifulSoup**: 网页内容提取

## 🔍 故障排除

### 常见问题

1. **前端无法访问**
   - 检查Node.js和npm版本
   - 确认3000端口未被占用
   - 运行 `npm install` 安装依赖

2. **后端API错误**
   - 检查Python版本和依赖
   - 确认5000端口未被占用
   - 检查DeepSeek API密钥配置

3. **语音功能异常**
   - 确认浏览器支持Web Speech API
   - 检查麦克风权限设置
   - 使用HTTPS访问（生产环境）

4. **文档上传失败**
   - 检查文件格式和大小限制
   - 确认uploads目录权限
   - 查看后端日志错误信息

### 性能优化

1. **向量检索优化**
   - 调整相似度阈值
   - 优化文档分块大小
   - 使用GPU加速（如可用）

2. **响应速度优化**
   - 启用缓存机制
   - 优化模型加载
   - 使用CDN加速静态资源

## 📞 技术支持

- **项目地址**: [GitHub Repository]
- **问题反馈**: [Issues]
- **技术文档**: [Wiki]
- **更新日志**: [CHANGELOG.md]

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目的支持：
- Vue.js 团队
- Flask 社区
- Hugging Face
- DeepSeek AI
- 所有贡献者

---

**🏥 家庭私人医生小帮手 - 让中医智慧触手可及**

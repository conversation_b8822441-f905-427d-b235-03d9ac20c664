#!/usr/bin/env python3
"""
测试系统集成功能
"""

import sys
sys.path.append('.')

def test_system_integration():
    print('🧪 测试系统集成功能...')
    
    try:
        # 测试主系统导入
        from perfect_unified_tcm_system import PerfectUnifiedTCMSystem
        print('✅ 主系统导入成功')
        
        # 创建系统实例
        app = PerfectUnifiedTCMSystem()
        print('✅ 系统实例创建成功')
        
        # 测试MCP系统
        mcp_available = app.mcp_system.available
        print(f'📡 MCP服务状态: {"可用" if mcp_available else "不可用"}')
        
        if mcp_available:
            # 测试MCP检索
            test_query = "肾虚怎么治疗"
            print(f'🔍 测试MCP检索: {test_query}')
            
            mcp_results = app.mcp_system.intelligent_search(test_query, max_results=2)
            if mcp_results:
                print(f'   ✅ MCP检索成功，返回 {len(mcp_results)} 个结果')
                for i, result in enumerate(mcp_results, 1):
                    title = result.get('title', '无标题')
                    score = result.get('score', 0)
                    print(f'   结果{i}: {title} (分数: {score})')
            else:
                print('   ❌ MCP检索无结果')
        
        # 测试智能回答生成器
        print('\n🤖 测试智能回答生成器...')
        app.response_generator.initialize()
        
        test_query = "失眠多梦怎么办"
        print(f'🔍 测试查询: {test_query}')
        
        response = app.response_generator.generate_response(test_query)
        if response:
            print('✅ 智能回答生成成功')
            print(f'回答长度: {len(response)} 字符')
            print(f'回答预览: {response[:200]}...')
        else:
            print('❌ 智能回答生成失败')
        
        # 测试语音管理器
        print('\n🎤 测试语音管理器...')
        voice_available = app.voice_manager.voice_available
        print(f'语音功能状态: {"可用" if voice_available else "不可用"}')
        
        # 测试文档上传管理器
        print('\n📁 测试文档上传管理器...')
        upload_history = app.upload_manager.get_upload_history()
        print(f'上传历史记录: {len(upload_history)} 条')
        
        # 测试聊天管理器
        print('\n💬 测试聊天管理器...')
        conversations = app.chat_manager.get_conversation_list()
        print(f'历史对话数量: {len(conversations)} 个')
        
        print('\n🎉 系统集成测试完成！')
        return True
        
    except Exception as e:
        print(f'❌ 系统集成测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_system_integration()

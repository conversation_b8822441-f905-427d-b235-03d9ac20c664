#!/usr/bin/env python3
"""
智能模型选择器
根据查询类型和性能要求自动选择最佳模型
"""

import logging
from typing import Optional, Dict, Any
from deepseek_ollama_api import DeepSeekOllamaAPI
from gemma3_ollama_api import Gemma3OllamaAPI

logger = logging.getLogger(__name__)

class IntelligentModelSelector:
    """智能模型选择器"""
    
    def __init__(self):
        self.deepseek_api = None
        self.gemma3_api = None
        self.model_performance = {}
        
        # 初始化模型
        self._initialize_models()
        
        # 查询类型分类
        self.query_categories = {
            'simple': ['是什么', '定义', '概念', '基础'],
            'complex': ['治疗', '方剂', '配伍', '诊断', '辨证'],
            'emergency': ['急救', '紧急', '危险', '严重']
        }
    
    def _initialize_models(self):
        """初始化模型"""
        try:
            # 初始化DeepSeek
            self.deepseek_api = DeepSeekOllamaAPI()
            if self.deepseek_api.available:
                logger.info("✅ DeepSeek模型初始化成功")
                self.model_performance['deepseek'] = {
                    'avg_response_time': 10.0,
                    'quality_score': 0.9,
                    'availability': True
                }
            else:
                logger.warning("⚠️ DeepSeek模型不可用")
                self.model_performance['deepseek'] = {
                    'availability': False
                }
        except Exception as e:
            logger.error(f"❌ DeepSeek初始化失败: {e}")
            self.model_performance['deepseek'] = {'availability': False}
        
        try:
            # 初始化Gemma 3
            self.gemma3_api = Gemma3OllamaAPI()
            if self.gemma3_api.available:
                logger.info("✅ Gemma 3模型初始化成功")
                self.model_performance['gemma3'] = {
                    'avg_response_time': 14.0,
                    'quality_score': 0.85,
                    'availability': True
                }
            else:
                logger.warning("⚠️ Gemma 3模型不可用")
                self.model_performance['gemma3'] = {
                    'availability': False
                }
        except Exception as e:
            logger.error(f"❌ Gemma 3初始化失败: {e}")
            self.model_performance['gemma3'] = {'availability': False}
    
    def categorize_query(self, query: str) -> str:
        """分类查询类型"""
        query_lower = query.lower()
        
        # 检查紧急类型
        for keyword in self.query_categories['emergency']:
            if keyword in query_lower:
                return 'emergency'
        
        # 检查复杂类型
        for keyword in self.query_categories['complex']:
            if keyword in query_lower:
                return 'complex'
        
        # 检查简单类型
        for keyword in self.query_categories['simple']:
            if keyword in query_lower:
                return 'simple'
        
        # 默认为复杂类型
        return 'complex'
    
    def select_best_model(self, query: str, priority: str = 'balanced') -> str:
        """选择最佳模型
        
        Args:
            query: 用户查询
            priority: 优先级 ('speed', 'quality', 'balanced')
        
        Returns:
            选择的模型名称
        """
        query_category = self.categorize_query(query)
        
        # 获取可用模型
        available_models = []
        if self.model_performance.get('deepseek', {}).get('availability', False):
            available_models.append('deepseek')
        if self.model_performance.get('gemma3', {}).get('availability', False):
            available_models.append('gemma3')
        
        if not available_models:
            logger.error("❌ 没有可用的模型")
            return None
        
        # 如果只有一个模型可用
        if len(available_models) == 1:
            return available_models[0]
        
        # 根据优先级和查询类型选择模型
        if priority == 'speed':
            # 优先选择速度快的模型
            if 'deepseek' in available_models:
                return 'deepseek'  # DeepSeek更快
            return available_models[0]
        
        elif priority == 'quality':
            # 优先选择质量高的模型
            if query_category == 'complex':
                # 复杂查询优先使用Gemma 3（更详细）
                if 'gemma3' in available_models:
                    return 'gemma3'
            # 其他情况使用DeepSeek
            if 'deepseek' in available_models:
                return 'deepseek'
            return available_models[0]
        
        else:  # balanced
            # 平衡模式：根据查询类型选择
            if query_category == 'simple':
                # 简单查询使用DeepSeek（更快）
                if 'deepseek' in available_models:
                    return 'deepseek'
            elif query_category == 'complex':
                # 复杂查询使用DeepSeek（性能更好）
                if 'deepseek' in available_models:
                    return 'deepseek'
            elif query_category == 'emergency':
                # 紧急查询使用最快的模型
                if 'deepseek' in available_models:
                    return 'deepseek'
            
            return available_models[0]
    
    def generate_response(self, 
                         query: str, 
                         context: str = "", 
                         priority: str = 'balanced',
                         max_tokens: int = 200,
                         temperature: float = 0.3) -> Optional[str]:
        """生成智能回答"""
        
        # 选择最佳模型
        selected_model = self.select_best_model(query, priority)
        
        if not selected_model:
            return "❌ 没有可用的AI模型，请检查系统配置"
        
        logger.info(f"🤖 选择模型: {selected_model} (查询类型: {self.categorize_query(query)}, 优先级: {priority})")
        
        try:
            # 使用选择的模型生成回答
            if selected_model == 'deepseek' and self.deepseek_api:
                return self.deepseek_api.generate_response(
                    query, context, max_tokens, temperature
                )
            elif selected_model == 'gemma3' and self.gemma3_api:
                return self.gemma3_api.generate_response(
                    query, context, max_tokens, temperature
                )
            else:
                return f"❌ 模型 {selected_model} 不可用"
                
        except Exception as e:
            logger.error(f"❌ 模型 {selected_model} 生成回答失败: {e}")
            
            # 尝试备用模型
            backup_models = [m for m in ['deepseek', 'gemma3'] if m != selected_model]
            for backup_model in backup_models:
                if self.model_performance.get(backup_model, {}).get('availability', False):
                    logger.info(f"🔄 尝试备用模型: {backup_model}")
                    try:
                        if backup_model == 'deepseek' and self.deepseek_api:
                            return self.deepseek_api.generate_response(
                                query, context, max_tokens, temperature
                            )
                        elif backup_model == 'gemma3' and self.gemma3_api:
                            return self.gemma3_api.generate_response(
                                query, context, max_tokens, temperature
                            )
                    except Exception as backup_e:
                        logger.error(f"❌ 备用模型 {backup_model} 也失败: {backup_e}")
                        continue
            
            return f"❌ 所有模型都不可用: {str(e)}"
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        return {
            'deepseek': {
                'available': self.model_performance.get('deepseek', {}).get('availability', False),
                'performance': self.model_performance.get('deepseek', {})
            },
            'gemma3': {
                'available': self.model_performance.get('gemma3', {}).get('availability', False),
                'performance': self.model_performance.get('gemma3', {})
            }
        }

# 测试函数
def test_intelligent_selector():
    """测试智能模型选择器"""
    print("🧪 测试智能模型选择器...")
    
    selector = IntelligentModelSelector()
    
    # 显示模型状态
    status = selector.get_model_status()
    print("📊 模型状态:")
    for model, info in status.items():
        print(f"   {model}: {'✅ 可用' if info['available'] else '❌ 不可用'}")
    
    # 测试不同类型的查询
    test_queries = [
        ("中医是什么", "simple"),
        ("失眠多梦怎么治疗", "complex"),
        ("急性心梗如何急救", "emergency")
    ]
    
    for query, expected_category in test_queries:
        print(f"\n🔍 测试查询: {query}")
        category = selector.categorize_query(query)
        print(f"   分类: {category} (预期: {expected_category})")
        
        selected_model = selector.select_best_model(query, 'balanced')
        print(f"   选择模型: {selected_model}")
        
        # 生成回答
        response = selector.generate_response(query, max_tokens=100)
        if response:
            print(f"   回答: {response[:100]}...")
        else:
            print("   回答: 生成失败")

if __name__ == "__main__":
    test_intelligent_selector()

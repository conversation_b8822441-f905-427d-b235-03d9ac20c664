{"name": "tcm-family-doctor-frontend", "version": "2.0.0", "description": "家庭私人医生小帮手 - Vue.js 前端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --port 3000"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "@popperjs/core": "^2.11.8", "bootstrap-icons": "^1.10.0", "mitt": "^3.0.1"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-router": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
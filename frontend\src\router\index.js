import { createRouter, createWebHistory } from 'vue-router'

// 路由组件
const ChatView = () => import('../views/ChatView.vue')
const DocumentsView = () => import('../views/DocumentsView.vue')
const SessionsView = () => import('../views/SessionsView.vue')
const SettingsView = () => import('../views/SettingsView.vue')

const routes = [
  {
    path: '/',
    name: 'Chat',
    component: ChatView,
    meta: {
      title: '智能咨询',
      icon: 'bi-chat-dots',
      description: '与AI医生进行智能对话咨询'
    }
  },
  {
    path: '/documents',
    name: 'Documents',
    component: DocumentsView,
    meta: {
      title: '文档管理',
      icon: 'bi-file-earmark-medical',
      description: '上传和管理医学文档'
    }
  },
  {
    path: '/sessions',
    name: 'Sessions',
    component: SessionsView,
    meta: {
      title: '历史记录',
      icon: 'bi-clock-history',
      description: '查看和管理对话历史'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: SettingsView,
    meta: {
      title: '系统设置',
      icon: 'bi-gear',
      description: '配置系统参数和偏好'
    }
  },
  {
    path: '/session/:sessionId',
    name: 'SessionDetail',
    component: ChatView,
    props: true,
    meta: {
      title: '对话详情',
      icon: 'bi-chat-square-text',
      description: '查看特定对话的详细内容'
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  const title = to.meta.title || '家庭私人医生小帮手'
  document.title = `${title} - 🏥 家庭私人医生小帮手`
  
  // 移动端路由切换时收起导航栏
  const navbarCollapse = document.querySelector('.navbar-collapse')
  if (navbarCollapse && navbarCollapse.classList.contains('show')) {
    const bsCollapse = new window.bootstrap.Collapse(navbarCollapse)
    bsCollapse.hide()
  }
  
  next()
})

router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)
})

export default router

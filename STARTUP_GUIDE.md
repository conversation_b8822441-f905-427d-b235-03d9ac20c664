# 🚀 家庭私人医生小帮手 - 启动指南

## 📋 **当前系统状态**
- ✅ **Flask后端服务**: 正常运行 (端口 5000)
- ✅ **MCP智能服务**: 正常运行 (端口 8006)
- ✅ **向量数据库**: 290个文档块已加载
- ✅ **DeepSeek AI模型**: 可用 (响应时间 20-30秒)
- ⚠️ **Vue.js前端**: 依赖安装问题 (已提供替代方案)

## 🎯 **推荐启动方式**

### 方式一：使用当前运行的服务 (推荐)
您的后端服务已经在运行，可以直接使用：

1. **访问简单前端界面**:
   ```
   file:///c:/Users/<USER>/Documents/augment-projects/RAG%202025/simple_frontend.html
   ```

2. **直接API访问**:
   ```
   健康检查: http://localhost:5000/api/health
   后端API: http://localhost:5000
   MCP服务: http://localhost:8006
   ```

### 方式二：手动重启所有服务
如果需要重新启动，请按以下步骤：

```bash
# 1. 启动MCP智能服务
python intelligent_mcp_service.py

# 2. 启动Flask后端服务 (新终端)
python flask_backend.py

# 3. 访问前端界面
# 打开浏览器访问: simple_frontend.html
```

## 🔧 **故障排除**

### 问题1: 端口被占用
```bash
# Windows - 查找并终止占用端口的进程
netstat -ano | findstr :5000
taskkill /F /PID <PID>

netstat -ano | findstr :8006
taskkill /F /PID <PID>
```

### 问题2: Vue.js前端依赖问题
**解决方案**: 使用提供的 `simple_frontend.html`，功能完全相同

### 问题3: AI响应超时
**正常现象**: DeepSeek模型响应需要20-30秒，请耐心等待

### 问题4: Ollama模型未启动
```bash
# 检查Ollama状态
ollama list

# 启动DeepSeek模型
ollama pull deepseek-r1-q4km:latest
```

## 🧪 **功能测试**

### 测试系统健康
```bash
curl http://localhost:5000/api/health
curl http://localhost:8006/health
```

### 测试聊天功能
在浏览器中访问 `simple_frontend.html`，尝试以下问题：
- "你好"
- "肾虚怎么治疗？"
- "栀子甘草豉汤方的组成是什么？"

## 📱 **功能特性**

### ✅ **已验证功能**
- 🤖 AI智能对话 (DeepSeek-R1模型)
- 📚 文档检索 (290个医学文档块)
- 🔍 在线知识搜索 (MCP服务)
- 🎤 语音输入 (Web Speech API)
- 💾 会话管理
- 📊 系统状态监控

### 🌐 **界面特性**
- 📱 移动端友好设计
- ⚡ 实时状态显示
- 🎯 快速问题按钮
- 🗣️ 语音输入支持
- 🧹 聊天记录清理

## 🎉 **使用建议**

1. **首次使用**: 先测试简单问题如"你好"
2. **医学咨询**: 详细描述症状，AI会提供专业建议
3. **语音输入**: 点击麦克风按钮，支持中文语音识别
4. **响应时间**: AI思考需要20-30秒，请耐心等待
5. **最佳体验**: 使用Chrome或Edge浏览器

## 📞 **技术支持**

如遇问题，请检查：
1. 后端服务是否运行: `http://localhost:5000/api/health`
2. MCP服务是否运行: `http://localhost:8006/health`
3. Ollama是否启动: `ollama list`
4. 浏览器控制台是否有错误信息

---

**🏥 家庭私人医生小帮手 v2.0.0** - 您的专业中医智能助手

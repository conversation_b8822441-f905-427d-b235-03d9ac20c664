import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// 导入Bootstrap CSS和JS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 创建Vue应用
const app = createApp(App)

// 全局配置
app.config.globalProperties.$apiBase = process.env.VUE_APP_API_BASE || 'http://localhost:5000'

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  // 可以在这里添加错误上报逻辑
}

// 使用路由
app.use(router)

// 挂载应用
app.mount('#app')

// 全局工具函数
window.formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

window.formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 检查浏览器兼容性
if (!window.EventSource) {
  console.warn('浏览器不支持Server-Sent Events，将使用轮询方式')
}

if (!window.SpeechRecognition && !window.webkitSpeechRecognition) {
  console.warn('浏览器不支持Web Speech API语音识别')
}

if (!window.speechSynthesis) {
  console.warn('浏览器不支持Web Speech API语音合成')
}

console.log('🏥 家庭私人医生小帮手前端已启动')
console.log('📱 移动端优化已启用')
console.log('🎤 语音功能检测完成')
console.log('🌐 API地址:', app.config.globalProperties.$apiBase)

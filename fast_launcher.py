#!/usr/bin/env python3
"""
快速启动器 - 优化版本
专注于快速启动和性能优化
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path

def check_port_available(port):
    """检查端口是否可用"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result != 0
    except:
        return True

def start_mcp_service():
    """启动MCP服务"""
    print('🔍 检查MCP服务状态...')
    
    # 检查服务是否已运行
    try:
        response = requests.get('http://localhost:8006/health', timeout=2)
        if response.status_code == 200:
            print('✅ MCP服务已运行')
            return True
    except:
        pass
    
    # 启动MCP服务
    print('🚀 启动MCP服务...')
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen([
                sys.executable, 'intelligent_mcp_service.py'
            ], creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            subprocess.Popen([
                sys.executable, 'intelligent_mcp_service.py'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 等待服务启动
        for i in range(10):
            time.sleep(1)
            try:
                response = requests.get('http://localhost:8006/health', timeout=2)
                if response.status_code == 200:
                    print('✅ MCP服务启动成功')
                    return True
            except:
                continue
        
        print('⚠️ MCP服务启动超时，但系统仍可使用')
        return False
        
    except Exception as e:
        print(f'❌ MCP服务启动失败: {e}')
        return False

def check_dependencies():
    """快速检查关键依赖"""
    print('📦 检查关键依赖...')
    
    critical_deps = ['streamlit', 'requests']
    missing_deps = []
    
    for dep in critical_deps:
        try:
            __import__(dep)
            print(f'   ✅ {dep}')
        except ImportError:
            missing_deps.append(dep)
            print(f'   ❌ {dep}')
    
    if missing_deps:
        print(f'❌ 缺少关键依赖: {", ".join(missing_deps)}')
        print('请运行: pip install -r requirements_perfect.txt')
        return False
    
    return True

def check_core_files():
    """检查核心文件"""
    print('📁 检查核心文件...')
    
    core_files = [
        'perfect_unified_tcm_system.py',
        'intelligent_rag_retriever.py',
        'intelligent_mcp_service.py',
        'deepseek_ollama_api.py'
    ]
    
    missing_files = []
    for file in core_files:
        if Path(file).exists():
            print(f'   ✅ {file}')
        else:
            missing_files.append(file)
            print(f'   ❌ {file}')
    
    if missing_files:
        print(f'❌ 缺少核心文件: {", ".join(missing_files)}')
        return False
    
    return True

def create_directories():
    """创建必要目录"""
    print('📁 创建必要目录...')
    
    dirs = ['documents', 'perfect_vector_db', 'conversations', 'uploads', 'models', 'logs', 'cache']
    
    for dir_name in dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f'   ✅ 创建 {dir_name}/')
        else:
            print(f'   ✅ {dir_name}/')

def start_streamlit():
    """启动Streamlit应用"""
    print('🌐 启动完美统一中医智能助手...')
    
    # 检查端口
    if not check_port_available(8501):
        print('⚠️ 端口8501被占用，尝试终止现有进程...')
        if os.name == 'nt':
            os.system('taskkill /f /im streamlit.exe 2>nul')
        else:
            os.system('pkill -f streamlit')
        time.sleep(2)
    
    # 启动Streamlit
    print('💡 系统将在浏览器中自动打开')
    print('🔗 访问地址: http://localhost:8501')
    print('')
    print('🎯 功能特色:')
    print('   ✅ 延迟加载组件，快速启动')
    print('   ✅ 智能RAG检索')
    print('   ✅ DeepSeek AI推理')
    print('   ✅ 语音交互功能')
    print('   ✅ 文档上传处理')
    print('')
    print('🧪 测试建议:')
    print('   1. 点击"初始化组件"按钮')
    print('   2. 输入: 失眠多梦怎么办')
    print('   3. 尝试语音输入功能')
    print('   4. 上传PDF文档测试')
    print('')
    
    # 启动命令
    cmd = [
        sys.executable, '-m', 'streamlit', 'run',
        'perfect_unified_tcm_system.py',
        '--server.headless', 'true',
        '--server.port', '8501',
        '--server.runOnSave', 'false',
        '--server.fileWatcherType', 'none'
    ]
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print('\n🛑 系统已停止')
    except Exception as e:
        print(f'❌ 启动失败: {e}')

def main():
    """主函数"""
    print('🏥 完美统一中医智能助手 - 快速启动器')
    print('🎯 优化版本 - 延迟加载，快速启动')
    print('=' * 60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查核心文件
    if not check_core_files():
        return
    
    # 创建目录
    create_directories()
    
    # 启动MCP服务
    start_mcp_service()
    
    # 启动Streamlit
    start_streamlit()

if __name__ == "__main__":
    main()

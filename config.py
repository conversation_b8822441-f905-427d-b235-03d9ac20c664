"""
RAG系统配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 模型配置
EMBEDDING_MODEL = "moka-ai/m3e-base"  # 中文嵌入模型
LLM_MODEL_PATH = PROJECT_ROOT / "models" / "llama-7b"  # Llama模型路径

# 文档存储配置
DOCUMENTS_DIR = PROJECT_ROOT / "documents"
VECTOR_DB_PATH = PROJECT_ROOT / "vector_db"
SESSIONS_DB_PATH = PROJECT_ROOT / "sessions.db"

# 模型下载URLs
MODEL_URLS = {
    "llama-7b": "https://huggingface.co/microsoft/DialoGPT-medium",  # 临时使用较小模型
    "m3e-base": "moka-ai/m3e-base"
}

# RAG配置 - 优化检索准确性
CHUNK_SIZE = 500  # 增大块大小以保持语义完整性
CHUNK_OVERLAP = 100  # 增加重叠以保持上下文连续性
TOP_K_RETRIEVAL = 5  # 增加检索数量以提高召回率
MAX_CONTEXT_LENGTH = 2048  # 增加上下文长度
MAX_CHUNKS_LIMIT = 1000  # 增加块数量限制

# 相似度阈值配置
SIMILARITY_THRESHOLD = 0.35  # 降低阈值以提高召回率
MIN_RELEVANCE_SCORE = 0.35  # 最小相关性分数
RERANK_THRESHOLD = 0.5  # 重排序阈值

# 批处理配置
BATCH_SIZE = 32  # 嵌入批处理大小
PAGE_BATCH_SIZE = 10  # PDF页面批处理大小

# 流式生成配置
STREAM_CHUNK_SIZE = 50
TEMPERATURE = 0.7
MAX_NEW_TOKENS = 256  # 减少生成长度

# 会话配置
MAX_HISTORY_LENGTH = 10
SESSION_TIMEOUT = 3600  # 1小时

# 创建必要目录
for path in [DOCUMENTS_DIR, VECTOR_DB_PATH, LLM_MODEL_PATH.parent]:
    path.mkdir(parents=True, exist_ok=True)

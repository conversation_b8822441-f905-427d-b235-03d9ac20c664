#!/usr/bin/env python3
"""
测试RAG检索器功能
"""

from intelligent_rag_retriever import IntelligentRAGRetriever
import logging

logging.basicConfig(level=logging.INFO)

def test_rag_retriever():
    print("🧪 测试RAG检索器...")
    
    # 初始化检索器
    retriever = IntelligentRAGRetriever()
    print("📦 正在初始化...")
    
    success = retriever.initialize()
    print(f"✅ 初始化结果: {success}")
    
    if not success:
        print("❌ 初始化失败")
        return False
    
    # 测试搜索
    test_queries = [
        "栀子甘草豉汤方",
        "肾虚怎么治疗",
        "失眠多梦"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: {query}")
        results = retriever.search(query, top_k=3)
        print(f"📊 找到结果: {len(results)} 个")
        
        for i, result in enumerate(results[:2], 1):
            score = result.get('combined_score', result.get('score', 0))
            content = result['content'][:100] + "..." if len(result['content']) > 100 else result['content']
            print(f"   {i}. 分数: {score:.3f}")
            print(f"      内容: {content}")
    
    return True

if __name__ == "__main__":
    test_rag_retriever()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 家庭私人医生小帮手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .chat-container { height: 400px; overflow-y: auto; }
        .message-user { background-color: #e3f2fd; }
        .message-ai { background-color: #f1f8e9; }
        .loading { opacity: 0.7; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- Header -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">
                            <i class="bi bi-heart-pulse-fill me-2"></i>
                            🏥 家庭私人医生小帮手 v2.0.0
                        </h3>
                        <small>Vue.js + Flask 架构 | 专业中医智能咨询</small>
                    </div>
                    <div class="card-body">
                        <div id="systemStatus" class="alert alert-info">
                            <i class="bi bi-hourglass-split me-2"></i>正在检查系统状态...
                        </div>
                    </div>
                </div>

                <!-- Chat Interface -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-dots-fill me-2"></i>智能医疗咨询
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Chat Messages -->
                        <div id="chatContainer" class="chat-container border rounded p-3 mb-3">
                            <div class="text-muted text-center">
                                <i class="bi bi-robot me-2"></i>
                                您好！我是您的私人中医助手，请告诉我您的症状或健康问题。
                            </div>
                        </div>

                        <!-- Input Area -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" id="messageInput" class="form-control" 
                                           placeholder="请输入您的症状或问题，例如：肾虚怎么治疗？" 
                                           onkeypress="handleKeyPress(event)">
                                    <button class="btn btn-success" onclick="sendMessage()" id="sendBtn">
                                        <i class="bi bi-send-fill me-1"></i>发送
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-primary me-2" onclick="startVoiceInput()" id="voiceBtn">
                                    <i class="bi bi-mic-fill me-1"></i>语音输入
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearChat()">
                                    <i class="bi bi-trash-fill me-1"></i>清空
                                </button>
                            </div>
                        </div>

                        <!-- Quick Questions -->
                        <div class="mt-3">
                            <small class="text-muted">常见问题：</small>
                            <div class="mt-2">
                                <button class="btn btn-outline-info btn-sm me-2 mb-1" onclick="quickQuestion('肾虚怎么治疗？')">肾虚治疗</button>
                                <button class="btn btn-outline-info btn-sm me-2 mb-1" onclick="quickQuestion('失眠多梦怎么办？')">失眠多梦</button>
                                <button class="btn btn-outline-info btn-sm me-2 mb-1" onclick="quickQuestion('栀子甘草豉汤方的组成是什么？')">栀子甘草豉汤</button>
                                <button class="btn btn-outline-info btn-sm me-2 mb-1" onclick="quickQuestion('脾胃虚弱的症状有哪些？')">脾胃虚弱</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Info -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle-fill me-2"></i>系统信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div id="flaskStatus" class="badge bg-secondary">检查中</div>
                                    <div class="small">Flask后端</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div id="mcpStatus" class="badge bg-secondary">检查中</div>
                                    <div class="small">MCP服务</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div id="vectorStatus" class="badge bg-secondary">检查中</div>
                                    <div class="small">向量数据库</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div id="aiStatus" class="badge bg-secondary">检查中</div>
                                    <div class="small">AI模型</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:5000';
        let sessionId = null;

        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                document.getElementById('systemStatus').innerHTML = `
                    <i class="bi bi-check-circle-fill text-success me-2"></i>
                    系统状态: ${data.status} | 向量数据库: ${data.vector_db_chunks} 个文档块 | 版本: ${data.version}
                `;
                document.getElementById('systemStatus').className = 'alert alert-success';
                
                // 更新状态指示器
                updateStatusBadge('flaskStatus', '正常', 'success');
                updateStatusBadge('vectorStatus', `${data.vector_db_chunks}块`, 'success');
                updateStatusBadge('aiStatus', data.components.deepseek_api ? '正常' : '异常', 
                                data.components.deepseek_api ? 'success' : 'danger');
                updateStatusBadge('mcpStatus', data.components.mcp_engine ? '正常' : '异常', 
                                data.components.mcp_engine ? 'success' : 'danger');
                
            } catch (error) {
                document.getElementById('systemStatus').innerHTML = `
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                    系统连接失败: ${error.message}
                `;
                document.getElementById('systemStatus').className = 'alert alert-danger';
                updateStatusBadge('flaskStatus', '失败', 'danger');
            }
        }

        function updateStatusBadge(id, text, type) {
            const element = document.getElementById(id);
            element.textContent = text;
            element.className = `badge bg-${type}`;
        }

        // 发送消息
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('请输入问题');
                return;
            }
            
            // 添加用户消息
            addMessage(message, 'user');
            messageInput.value = '';
            
            // 显示AI思考状态
            const thinkingId = addMessage('AI医生正在分析您的问题，请稍候...', 'ai', true);
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>处理中';
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        message: message,
                        session_id: sessionId 
                    })
                });
                
                const endTime = Date.now();
                const responseTime = (endTime - startTime) / 1000;
                
                // 移除思考消息
                document.getElementById(thinkingId).remove();
                
                if (response.ok) {
                    const data = await response.json();
                    sessionId = data.session_id;
                    
                    // 添加AI回复
                    const aiResponse = `${data.response}\n\n<small class="text-muted"><i class="bi bi-clock me-1"></i>响应时间: ${responseTime.toFixed(2)}秒</small>`;
                    addMessage(aiResponse, 'ai');
                    
                } else {
                    const errorData = await response.json();
                    addMessage(`抱歉，系统出现问题：${errorData.error || response.statusText}`, 'ai');
                }
                
            } catch (error) {
                document.getElementById(thinkingId).remove();
                addMessage(`网络连接错误：${error.message}`, 'ai');
            } finally {
                // 恢复发送按钮
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<i class="bi bi-send-fill me-1"></i>发送';
            }
        }

        // 添加消息到聊天容器
        function addMessage(content, type, isTemporary = false) {
            const chatContainer = document.getElementById('chatContainer');
            const messageId = 'msg_' + Date.now();
            
            const messageDiv = document.createElement('div');
            messageDiv.id = messageId;
            messageDiv.className = `mb-3 p-3 rounded ${type === 'user' ? 'message-user' : 'message-ai'}`;
            
            const icon = type === 'user' ? 'person-fill' : 'robot';
            const label = type === 'user' ? '您' : 'AI医生';
            
            messageDiv.innerHTML = `
                <div class="d-flex align-items-start">
                    <i class="bi bi-${icon} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <strong>${label}:</strong>
                        <div class="mt-1">${content.replace(/\n/g, '<br>')}</div>
                    </div>
                </div>
            `;
            
            if (isTemporary) {
                messageDiv.classList.add('loading');
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            return messageId;
        }

        // 快速问题
        function quickQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        // 清空聊天
        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = `
                <div class="text-muted text-center">
                    <i class="bi bi-robot me-2"></i>
                    您好！我是您的私人中医助手，请告诉我您的症状或健康问题。
                </div>
            `;
            sessionId = null;
        }

        // 语音输入
        function startVoiceInput() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();
                
                recognition.lang = 'zh-CN';
                recognition.continuous = false;
                recognition.interimResults = false;
                
                const voiceBtn = document.getElementById('voiceBtn');
                voiceBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>听取中';
                voiceBtn.disabled = true;
                
                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                };
                
                recognition.onend = function() {
                    voiceBtn.innerHTML = '<i class="bi bi-mic-fill me-1"></i>语音输入';
                    voiceBtn.disabled = false;
                };
                
                recognition.onerror = function(event) {
                    alert('语音识别失败：' + event.error);
                    voiceBtn.innerHTML = '<i class="bi bi-mic-fill me-1"></i>语音输入';
                    voiceBtn.disabled = false;
                };
                
                recognition.start();
            } else {
                alert('您的浏览器不支持语音识别功能');
            }
        }

        // 回车发送
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载时检查系统状态
        window.onload = function() {
            checkSystemStatus();
        };
    </script>
</body>
</html>

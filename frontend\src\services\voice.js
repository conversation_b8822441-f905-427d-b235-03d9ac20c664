/**
 * 语音服务类 - 集成Web Speech API和后端语音功能
 * 支持语音识别和语音合成
 */

import apiService from './api'

class VoiceService {
  constructor() {
    // 语音识别相关
    this.recognition = null
    this.isRecognitionSupported = this._checkRecognitionSupport()
    this.isRecognizing = false
    
    // 语音合成相关
    this.synthesis = window.speechSynthesis
    this.isSynthesisSupported = !!this.synthesis
    this.currentUtterance = null
    this.isSpeaking = false
    
    // 配置参数
    this.config = {
      recognition: {
        language: 'zh-CN',
        continuous: false,
        interimResults: false,
        maxAlternatives: 1,
        timeout: 10000,
        phraseTimeLimit: 15000
      },
      synthesis: {
        language: 'zh-CN',
        rate: 0.9,
        pitch: 1.0,
        volume: 0.8
      }
    }
    
    this._initializeRecognition()
    this._initializeSynthesis()
  }

  // ==================== 语音识别功能 ====================

  _checkRecognitionSupport() {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition)
  }

  _initializeRecognition() {
    if (!this.isRecognitionSupported) {
      console.warn('浏览器不支持语音识别功能')
      return
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    this.recognition = new SpeechRecognition()
    
    // 配置语音识别
    this.recognition.lang = this.config.recognition.language
    this.recognition.continuous = this.config.recognition.continuous
    this.recognition.interimResults = this.config.recognition.interimResults
    this.recognition.maxAlternatives = this.config.recognition.maxAlternatives
  }

  /**
   * 开始语音识别
   * @returns {Promise<string>} 识别结果
   */
  recognize() {
    return new Promise((resolve, reject) => {
      if (!this.isRecognitionSupported) {
        reject(new Error('浏览器不支持语音识别'))
        return
      }

      if (this.isRecognizing) {
        reject(new Error('语音识别正在进行中'))
        return
      }

      this.isRecognizing = true
      let timeoutId = null

      // 设置超时
      timeoutId = setTimeout(() => {
        this.recognition.stop()
        this.isRecognizing = false
        reject(new Error('语音识别超时'))
      }, this.config.recognition.timeout)

      // 识别成功
      this.recognition.onresult = (event) => {
        clearTimeout(timeoutId)
        this.isRecognizing = false
        
        const result = event.results[0][0].transcript
        const confidence = event.results[0][0].confidence
        
        console.log('语音识别结果:', result, '置信度:', confidence)
        resolve(result)
      }

      // 识别错误
      this.recognition.onerror = (event) => {
        clearTimeout(timeoutId)
        this.isRecognizing = false
        
        let errorMessage = '语音识别失败'
        switch (event.error) {
          case 'no-speech':
            errorMessage = '未检测到语音输入'
            break
          case 'audio-capture':
            errorMessage = '无法访问麦克风'
            break
          case 'not-allowed':
            errorMessage = '麦克风访问被拒绝'
            break
          case 'network':
            errorMessage = '网络错误'
            break
          case 'service-not-allowed':
            errorMessage = '语音识别服务不可用'
            break
          default:
            errorMessage = `语音识别错误: ${event.error}`
        }
        
        console.error('语音识别错误:', event.error)
        reject(new Error(errorMessage))
      }

      // 识别结束
      this.recognition.onend = () => {
        this.isRecognizing = false
      }

      try {
        this.recognition.start()
        console.log('开始语音识别...')
      } catch (error) {
        clearTimeout(timeoutId)
        this.isRecognizing = false
        reject(new Error('启动语音识别失败'))
      }
    })
  }

  /**
   * 停止语音识别
   */
  stopRecognition() {
    if (this.recognition && this.isRecognizing) {
      this.recognition.stop()
      this.isRecognizing = false
    }
  }

  /**
   * 检查是否支持语音识别
   */
  isRecognitionSupported() {
    return this.isRecognitionSupported
  }

  // ==================== 语音合成功能 ====================

  _initializeSynthesis() {
    if (!this.isSynthesisSupported) {
      console.warn('浏览器不支持语音合成功能')
      return
    }

    // 等待语音列表加载
    if (this.synthesis.getVoices().length === 0) {
      this.synthesis.addEventListener('voiceschanged', () => {
        this._selectChineseVoice()
      })
    } else {
      this._selectChineseVoice()
    }
  }

  _selectChineseVoice() {
    const voices = this.synthesis.getVoices()
    
    // 优先选择中文语音
    const chineseVoices = voices.filter(voice => 
      voice.lang.includes('zh') || 
      voice.name.includes('Chinese') ||
      voice.name.includes('中文')
    )

    if (chineseVoices.length > 0) {
      this.selectedVoice = chineseVoices[0]
      console.log('选择语音:', this.selectedVoice.name, this.selectedVoice.lang)
    } else {
      console.warn('未找到中文语音，使用默认语音')
      this.selectedVoice = voices[0] || null
    }
  }

  /**
   * 语音播放文本
   * @param {string} text 要播放的文本
   * @returns {Promise<void>}
   */
  speak(text) {
    return new Promise((resolve, reject) => {
      if (!text || text.trim() === '') {
        reject(new Error('文本内容为空'))
        return
      }

      // 优先使用后端语音服务
      this._speakWithBackend(text)
        .then(resolve)
        .catch(() => {
          // 后端失败时使用浏览器语音合成
          console.log('后端语音服务不可用，使用浏览器语音合成')
          this._speakWithBrowser(text).then(resolve).catch(reject)
        })
    })
  }

  /**
   * 使用后端语音服务
   */
  async _speakWithBackend(text) {
    try {
      await apiService.speakText(text)
      console.log('后端语音播放成功')
    } catch (error) {
      console.error('后端语音播放失败:', error)
      throw error
    }
  }

  /**
   * 使用浏览器语音合成
   */
  _speakWithBrowser(text) {
    return new Promise((resolve, reject) => {
      if (!this.isSynthesisSupported) {
        reject(new Error('浏览器不支持语音合成'))
        return
      }

      // 停止当前播放
      this.stop()

      // 清理文本
      const cleanText = this._cleanTextForSpeech(text)
      
      // 创建语音对象
      this.currentUtterance = new SpeechSynthesisUtterance(cleanText)
      
      // 配置语音参数
      if (this.selectedVoice) {
        this.currentUtterance.voice = this.selectedVoice
      }
      this.currentUtterance.lang = this.config.synthesis.language
      this.currentUtterance.rate = this.config.synthesis.rate
      this.currentUtterance.pitch = this.config.synthesis.pitch
      this.currentUtterance.volume = this.config.synthesis.volume

      // 事件处理
      this.currentUtterance.onstart = () => {
        this.isSpeaking = true
        console.log('开始语音播放')
      }

      this.currentUtterance.onend = () => {
        this.isSpeaking = false
        this.currentUtterance = null
        console.log('语音播放完成')
        resolve()
      }

      this.currentUtterance.onerror = (event) => {
        this.isSpeaking = false
        this.currentUtterance = null
        console.error('语音播放错误:', event.error)
        reject(new Error(`语音播放失败: ${event.error}`))
      }

      // 开始播放
      try {
        this.synthesis.speak(this.currentUtterance)
      } catch (error) {
        this.isSpeaking = false
        this.currentUtterance = null
        reject(new Error('启动语音播放失败'))
      }
    })
  }

  /**
   * 清理文本用于语音播放
   */
  _cleanTextForSpeech(text) {
    return text
      .replace(/[#*`\[\]()]/g, '') // 移除Markdown符号
      .replace(/https?:\/\/\S+/g, '') // 移除URL
      .replace(/[🏥🔍📋💊⚠️📚🎤🔊]/g, '') // 移除emoji
      .replace(/\n+/g, ' ') // 换行转空格
      .replace(/\s+/g, ' ') // 多个空格合并
      .trim()
      .substring(0, 300) // 限制长度
  }

  /**
   * 停止语音播放
   */
  stop() {
    if (this.synthesis && this.isSpeaking) {
      this.synthesis.cancel()
      this.isSpeaking = false
      this.currentUtterance = null
    }
  }

  /**
   * 暂停语音播放
   */
  pause() {
    if (this.synthesis && this.isSpeaking) {
      this.synthesis.pause()
    }
  }

  /**
   * 恢复语音播放
   */
  resume() {
    if (this.synthesis) {
      this.synthesis.resume()
    }
  }

  /**
   * 检查是否支持语音合成
   */
  isSynthesisSupported() {
    return this.isSynthesisSupported
  }

  /**
   * 检查是否正在播放
   */
  isSpeaking() {
    return this.isSpeaking
  }

  /**
   * 获取可用的语音列表
   */
  getVoices() {
    return this.synthesis ? this.synthesis.getVoices() : []
  }

  /**
   * 设置语音参数
   */
  setConfig(config) {
    this.config = { ...this.config, ...config }
    
    if (this.recognition) {
      this.recognition.lang = this.config.recognition.language
      this.recognition.continuous = this.config.recognition.continuous
      this.recognition.interimResults = this.config.recognition.interimResults
    }
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config }
  }
}

export default VoiceService

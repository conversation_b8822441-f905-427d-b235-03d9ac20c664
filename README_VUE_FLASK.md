# 🏥 家庭私人医生小帮手 v2.0.0

> **专业中医智能咨询系统** | Vue.js + Flask 架构 | 支持语音交互 | 移动端优化

## 📋 系统概述

家庭私人医生小帮手是一个基于Vue.js + Flask架构的现代化中医智能咨询系统，采用前后端分离设计，集成了RAG（检索增强生成）技术、实时通信、语音交互等先进功能，为用户提供专业、准确、便捷的中医健康咨询服务。

### 🎯 核心特性

- **🌐 现代架构**: Vue.js 3.x + Flask RESTful API，前后端分离设计
- **🤖 智能对话**: 基于DeepSeek-R1模型的专业中医咨询
- **📚 文档检索**: 支持PDF、TXT、DOCX等格式的医学文档上传和检索
- **🔍 在线搜索**: 集成MCP服务，实时检索古代医书知识
- **🎤 语音交互**: Web Speech API + 后端语音服务双重支持
- **📱 移动优化**: Bootstrap 5.x响应式设计，完美适配移动设备
- **⚡ 实时通信**: Server-Sent Events (SSE) 流式对话体验
- **💾 会话管理**: 完整的对话历史记录和会话管理功能
- **🌐 远程访问**: 支持ngrok隧道，实现远程访问能力

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    🏥 家庭私人医生小帮手 v2.0.0                    │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue.js 3.x + Bootstrap 5.x)                        │
│  ├── 智能咨询界面 (ChatView)                                  │
│  ├── 文档管理界面 (DocumentsView)                             │
│  ├── 历史记录界面 (SessionsView)                              │
│  ├── 系统设置界面 (SettingsView)                              │
│  └── 语音交互服务 (VoiceService)                              │
├─────────────────────────────────────────────────────────────┤
│  API层 (Flask RESTful API)                                  │
│  ├── /api/chat - 聊天接口                                    │
│  ├── /api/chat/stream - 流式聊天接口                          │
│  ├── /api/sessions - 会话管理接口                             │
│  ├── /api/voice - 语音功能接口                                │
│  ├── /api/documents - 文档管理接口                            │
│  └── /api/health - 系统健康检查                               │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                   │
│  ├── 智能响应生成器 (ResponseGenerator)                       │
│  ├── 会话管理器 (SessionManager)                              │
│  ├── 文档上传管理器 (DocumentUploadManager)                   │
│  └── 语音管理器 (VoiceManager)                                │
├─────────────────────────────────────────────────────────────┤
│  核心服务层                                                   │
│  ├── RAG检索器 (IntelligentRAGRetriever)                     │
│  ├── MCP智能服务 (IntelligentMCPService)                     │
│  ├── DeepSeek API (DeepSeekOllamaAPI)                       │
│  └── 向量数据库 (FAISS + m3e-base)                           │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
RAG 2025/
├── 📄 perfect_startup.py              # 统一启动脚本
├── 📄 flask_backend.py                # Flask后端服务
├── 📄 intelligent_rag_retriever.py    # RAG检索器
├── 📄 intelligent_mcp_service.py      # MCP智能服务
├── 📄 deepseek_ollama_api.py          # DeepSeek API集成
├── 📄 requirements_perfect.txt        # Python依赖
├── 📄 README_VUE_FLASK.md            # 本文档
├── 📁 frontend/                       # Vue.js前端
│   ├── 📄 package.json               # 前端依赖配置
│   ├── 📁 public/                    # 静态资源
│   │   └── 📄 index.html             # 主页面模板
│   └── 📁 src/                       # 源代码
│       ├── 📄 main.js                # 应用入口
│       ├── 📄 App.vue                # 根组件
│       ├── 📁 router/                # 路由配置
│       ├── 📁 views/                 # 页面组件
│       │   ├── 📄 ChatView.vue       # 智能咨询页面
│       │   ├── 📄 DocumentsView.vue  # 文档管理页面
│       │   ├── 📄 SessionsView.vue   # 历史记录页面
│       │   └── 📄 SettingsView.vue   # 系统设置页面
│       └── 📁 services/              # 服务层
│           ├── 📄 api.js             # API服务
│           └── 📄 voice.js           # 语音服务
├── 📁 perfect_vector_db/             # 向量数据库
├── 📁 documents/                     # 系统文档
├── 📁 uploads/                       # 用户上传文档
├── 📁 conversations/                 # 对话历史
└── 📁 models/                        # AI模型文件
```

## 🚀 快速开始

### 1. 环境要求

- **Python**: 3.8+
- **Node.js**: 16.0+
- **npm**: 8.0+
- **Ollama**: 最新版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd "RAG 2025"

# 安装Python依赖
pip install -r requirements_perfect.txt

# 安装前端依赖
cd frontend
npm install
cd ..

# 确保Ollama运行并加载DeepSeek模型
ollama pull deepseek-r1-q4km:latest
```

### 3. 启动系统

```bash
# 一键启动所有服务
python perfect_startup.py
```

启动成功后，系统会自动：
- 启动Flask后端服务 (端口5000)
- 启动MCP智能服务 (端口8006)  
- 启动Vue.js前端服务 (端口3000)
- 打开浏览器访问 http://localhost:3000

### 4. 访问系统

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:5000
- **MCP服务**: http://localhost:8006
- **API文档**: http://localhost:5000/api/health

## 🔧 API接口文档

### 聊天接口

#### POST /api/chat
发送消息并获取AI回答

**请求体**:
```json
{
  "message": "肾虚怎么治疗？",
  "session_id": "session_20241223_143022_abc123"
}
```

**响应**:
```json
{
  "response": "肾虚的治疗需要根据具体症状...",
  "session_id": "session_20241223_143022_abc123",
  "response_time": 15.2,
  "timestamp": "2024-12-23T14:30:22.123Z"
}
```

#### POST /api/chat/stream
流式聊天接口，支持Server-Sent Events

**请求体**: 同上

**响应**: SSE流式数据
```
data: {"type": "start", "session_id": "session_xxx"}
data: {"type": "processing", "message": "正在检索相关资料..."}
data: {"type": "response", "content": "肾虚的治疗..."}
data: {"type": "complete", "timestamp": "2024-12-23T14:30:22.123Z"}
```

### 会话管理接口

#### GET /api/sessions
获取会话列表

#### GET /api/sessions/{session_id}
获取特定会话详情

#### POST /api/sessions
创建新会话

#### DELETE /api/sessions/{session_id}
删除会话

### 语音功能接口

#### POST /api/voice/speak
文本转语音

**请求体**:
```json
{
  "text": "这是要播放的文本内容"
}
```

#### POST /api/voice/recognize
语音识别

**请求体**:
```json
{
  "timeout": 10,
  "phrase_time_limit": 15
}
```

### 文档管理接口

#### POST /api/documents/upload
上传文档

**请求**: multipart/form-data格式，支持多文件上传

#### GET /api/documents
获取文档列表

### 系统监控接口

#### GET /api/health
系统健康检查

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-23T14:30:22.123Z",
  "components": {
    "rag_retriever": true,
    "deepseek_api": true,
    "mcp_engine": true,
    "voice_engine": true
  },
  "vector_db_chunks": 290,
  "service": "家庭私人医生小帮手",
  "version": "2.0.0-vue-flask"
}
```

## 🎤 语音功能

### Web Speech API支持
- **语音识别**: 支持中文语音输入
- **语音合成**: 支持中文语音播放
- **浏览器兼容**: Chrome, Edge, Safari等主流浏览器

### 后端语音服务
- **pyttsx3**: 离线文本转语音
- **SpeechRecognition**: 语音识别引擎
- **双重保障**: 前端+后端语音功能互补

## 📱 移动端优化

### 响应式设计
- **Bootstrap 5.x**: 现代化响应式框架
- **移动优先**: 专为移动设备优化的界面
- **触摸友好**: 适配触摸操作的交互设计

### PWA支持
- **离线缓存**: 支持离线访问核心功能
- **安装到桌面**: 可安装为原生应用
- **推送通知**: 支持消息推送功能

## 🔒 安全特性

### 数据安全
- **本地存储**: 所有数据存储在本地，保护隐私
- **HTTPS支持**: 支持SSL/TLS加密传输
- **输入验证**: 严格的输入验证和过滤

### 访问控制
- **会话管理**: 安全的会话管理机制
- **CORS配置**: 合理的跨域资源共享配置
- **错误处理**: 完善的错误处理和日志记录

## 🛠️ 开发指南

### 前端开发

```bash
# 进入前端目录
cd frontend

# 开发模式启动
npm run serve

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

### 后端开发

```bash
# 启动Flask开发服务器
python flask_backend.py

# 启动MCP服务
python intelligent_mcp_service.py

# 运行测试
python -m pytest tests/
```

### 调试模式

```bash
# 启用调试模式
export FLASK_ENV=development
export VUE_APP_DEBUG=true

# 启动系统
python perfect_startup.py
```

## 📊 性能指标

### 系统要求
- **响应时间**: AI回答 < 30秒
- **页面加载**: 首屏加载 < 3秒
- **内存使用**: < 2GB RAM
- **存储空间**: < 5GB 磁盘空间

### 性能优化
- **懒加载**: 组件和资源按需加载
- **缓存策略**: 智能缓存机制
- **压缩优化**: 代码和资源压缩
- **CDN支持**: 静态资源CDN加速

## 🐛 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # Windows
   netstat -ano | findstr :5000
   taskkill /F /PID <PID>
   
   # Linux/macOS
   lsof -ti:5000 | xargs kill -9
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存重新安装
   pip cache purge
   pip install -r requirements_perfect.txt --no-cache-dir
   
   # 前端依赖
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **模型加载失败**
   ```bash
   # 检查Ollama状态
   ollama list
   ollama pull deepseek-r1-q4km:latest
   ```

### 日志查看

```bash
# 查看系统日志
tail -f perfect_system.log

# 查看Flask日志
tail -f flask.log

# 查看前端日志
# 浏览器开发者工具 -> Console
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

### 代码规范
- **Python**: PEP 8
- **JavaScript**: ESLint Standard
- **Vue.js**: Vue Style Guide
- **提交信息**: Conventional Commits

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🙏 致谢

感谢以下开源项目的支持：
- Vue.js - 渐进式JavaScript框架
- Flask - 轻量级Python Web框架
- Bootstrap - 响应式CSS框架
- DeepSeek - 智能语言模型
- Ollama - 本地AI模型运行环境

---

**🏥 家庭私人医生小帮手 v2.0.0** - 让中医智慧触手可及

#!/usr/bin/env python3
"""
测试语音功能
"""

try:
    from perfect_unified_tcm_system import VoiceManager
    
    print("🎤 测试语音功能...")
    
    # 初始化语音管理器
    vm = VoiceManager()
    print(f"✅ 语音功能可用: {vm.voice_available}")
    
    if vm.voice_available:
        print("🔊 测试文本转语音...")
        success = vm.speak_text("这是中医智能助手的语音测试")
        print(f"✅ TTS测试结果: {success}")
    else:
        print("⚠️ 语音功能不可用，可能缺少依赖包")
        print("请安装: pip install pyttsx3 SpeechRecognition")
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")

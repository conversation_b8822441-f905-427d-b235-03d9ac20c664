<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta name="description" content="家庭私人医生小帮手 - 专业中医智能咨询系统">
    <meta name="keywords" content="中医,智能咨询,家庭医生,健康助手">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>🏥 家庭私人医生小帮手</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 移动端优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="家庭医生助手">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="<%= BASE_URL %>manifest.json">
    <meta name="theme-color" content="#198754">
    
    <style>
      /* 全局样式 */
      body {
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
      }
      
      /* 移动端适配 */
      @media (max-width: 768px) {
        .container-fluid {
          padding: 0.5rem;
        }
        
        .card {
          border-radius: 0.75rem;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
          border-radius: 0.5rem;
        }
      }
      
      /* 聊天界面样式 */
      .chat-container {
        height: 70vh;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        background: white;
        padding: 1rem;
      }
      
      .message-user {
        background: #007bff;
        color: white;
        border-radius: 1rem 1rem 0.25rem 1rem;
        padding: 0.75rem 1rem;
        margin: 0.5rem 0;
        margin-left: 20%;
        word-wrap: break-word;
      }
      
      .message-assistant {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        border-radius: 1rem 1rem 1rem 0.25rem;
        padding: 0.75rem 1rem;
        margin: 0.5rem 0;
        margin-right: 20%;
        word-wrap: break-word;
      }
      
      .message-system {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin: 0.5rem 0;
        text-align: center;
        font-size: 0.9rem;
      }
      
      /* 加载动画 */
      .typing-indicator {
        display: flex;
        align-items: center;
        padding: 1rem;
        color: #6c757d;
      }
      
      .typing-dots {
        display: flex;
        gap: 0.25rem;
        margin-left: 0.5rem;
      }
      
      .typing-dot {
        width: 0.5rem;
        height: 0.5rem;
        background: #6c757d;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
      }
      
      .typing-dot:nth-child(1) { animation-delay: -0.32s; }
      .typing-dot:nth-child(2) { animation-delay: -0.16s; }
      
      @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
      }
      
      /* 语音按钮样式 */
      .voice-btn {
        border-radius: 50%;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }
      
      .voice-btn:hover {
        transform: scale(1.1);
      }
      
      .voice-btn.recording {
        background: #dc3545 !important;
        animation: pulse 1s infinite;
      }
      
      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>抱歉，家庭私人医生小帮手需要启用JavaScript才能正常工作。请启用JavaScript并刷新页面。</strong>
    </noscript>
    <div id="app"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>

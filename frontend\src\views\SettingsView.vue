<template>
  <div class="settings-view">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-secondary text-white">
            <div class="d-flex align-items-center">
              <i class="bi bi-gear me-2"></i>
              <h5 class="mb-0">⚙️ 系统设置</h5>
            </div>
          </div>

          <div class="card-body">
            <div class="row">
              <!-- 语音设置 -->
              <div class="col-lg-6 mb-4">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">
                      <i class="bi bi-mic me-2"></i>语音设置
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="voiceEnabled"
                          v-model="settings.voice.enabled"
                        >
                        <label class="form-check-label" for="voiceEnabled">
                          启用语音功能
                        </label>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="autoSpeak"
                          v-model="settings.voice.autoSpeak"
                          :disabled="!settings.voice.enabled"
                        >
                        <label class="form-check-label" for="autoSpeak">
                          自动朗读AI回答
                        </label>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="speechRate" class="form-label">语音速度</label>
                      <input 
                        type="range" 
                        class="form-range" 
                        id="speechRate"
                        min="0.5" 
                        max="2" 
                        step="0.1"
                        v-model="settings.voice.rate"
                        :disabled="!settings.voice.enabled"
                      >
                      <div class="d-flex justify-content-between">
                        <small class="text-muted">慢</small>
                        <small class="text-muted">{{ settings.voice.rate }}x</small>
                        <small class="text-muted">快</small>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="speechVolume" class="form-label">音量</label>
                      <input 
                        type="range" 
                        class="form-range" 
                        id="speechVolume"
                        min="0" 
                        max="1" 
                        step="0.1"
                        v-model="settings.voice.volume"
                        :disabled="!settings.voice.enabled"
                      >
                      <div class="d-flex justify-content-between">
                        <small class="text-muted">静音</small>
                        <small class="text-muted">{{ Math.round(settings.voice.volume * 100) }}%</small>
                        <small class="text-muted">最大</small>
                      </div>
                    </div>

                    <button 
                      class="btn btn-outline-primary btn-sm"
                      @click="testVoice"
                      :disabled="!settings.voice.enabled || isTesting"
                    >
                      <i class="bi bi-play-circle me-1"></i>测试语音
                    </button>
                  </div>
                </div>
              </div>

              <!-- 界面设置 -->
              <div class="col-lg-6 mb-4">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">
                      <i class="bi bi-palette me-2"></i>界面设置
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label for="theme" class="form-label">主题</label>
                      <select class="form-select" id="theme" v-model="settings.ui.theme">
                        <option value="light">浅色主题</option>
                        <option value="dark">深色主题</option>
                        <option value="auto">跟随系统</option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <label for="fontSize" class="form-label">字体大小</label>
                      <select class="form-select" id="fontSize" v-model="settings.ui.fontSize">
                        <option value="small">小</option>
                        <option value="medium">中</option>
                        <option value="large">大</option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="compactMode"
                          v-model="settings.ui.compactMode"
                        >
                        <label class="form-check-label" for="compactMode">
                          紧凑模式
                        </label>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="showTimestamp"
                          v-model="settings.ui.showTimestamp"
                        >
                        <label class="form-check-label" for="showTimestamp">
                          显示时间戳
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- AI设置 -->
              <div class="col-lg-6 mb-4">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">
                      <i class="bi bi-robot me-2"></i>AI设置
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label for="responseLength" class="form-label">回答长度偏好</label>
                      <select class="form-select" id="responseLength" v-model="settings.ai.responseLength">
                        <option value="short">简洁</option>
                        <option value="medium">适中</option>
                        <option value="detailed">详细</option>
                      </select>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="enableContext"
                          v-model="settings.ai.enableContext"
                        >
                        <label class="form-check-label" for="enableContext">
                          启用上下文记忆
                        </label>
                      </div>
                    </div>

                    <div class="mb-3">
                      <div class="form-check form-switch">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          id="enableMCP"
                          v-model="settings.ai.enableMCP"
                        >
                        <label class="form-check-label" for="enableMCP">
                          启用在线知识检索
                        </label>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="maxTokens" class="form-label">最大回答长度</label>
                      <input 
                        type="range" 
                        class="form-range" 
                        id="maxTokens"
                        min="100" 
                        max="1000" 
                        step="50"
                        v-model="settings.ai.maxTokens"
                      >
                      <div class="d-flex justify-content-between">
                        <small class="text-muted">100</small>
                        <small class="text-muted">{{ settings.ai.maxTokens }} tokens</small>
                        <small class="text-muted">1000</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 系统信息 -->
              <div class="col-lg-6 mb-4">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">
                      <i class="bi bi-info-circle me-2"></i>系统信息
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-2">
                      <strong>版本:</strong> {{ systemInfo.version }}
                    </div>
                    <div class="mb-2">
                      <strong>API地址:</strong> {{ systemInfo.apiUrl }}
                    </div>
                    <div class="mb-2">
                      <strong>连接状态:</strong> 
                      <span :class="systemInfo.connected ? 'text-success' : 'text-danger'">
                        {{ systemInfo.connected ? '已连接' : '未连接' }}
                      </span>
                    </div>
                    <div class="mb-2">
                      <strong>响应延迟:</strong> {{ systemInfo.latency }}ms
                    </div>
                    <div class="mb-2">
                      <strong>浏览器:</strong> {{ systemInfo.browser }}
                    </div>
                    <div class="mb-3">
                      <strong>语音支持:</strong> 
                      <span :class="systemInfo.voiceSupport ? 'text-success' : 'text-warning'">
                        {{ systemInfo.voiceSupport ? '支持' : '部分支持' }}
                      </span>
                    </div>

                    <button 
                      class="btn btn-outline-info btn-sm me-2"
                      @click="checkConnection"
                      :disabled="isChecking"
                    >
                      <i class="bi bi-arrow-clockwise me-1"></i>检查连接
                    </button>

                    <button 
                      class="btn btn-outline-secondary btn-sm"
                      @click="exportSettings"
                    >
                      <i class="bi bi-download me-1"></i>导出设置
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="row">
              <div class="col-12">
                <div class="card">
                  <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 class="mb-1">设置管理</h6>
                        <small class="text-muted">保存或重置您的个人设置</small>
                      </div>
                      <div class="btn-group">
                        <button 
                          class="btn btn-success"
                          @click="saveSettings"
                          :disabled="isSaving"
                        >
                          <i class="bi bi-check-circle me-1"></i>保存设置
                        </button>
                        <button 
                          class="btn btn-outline-warning"
                          @click="resetSettings"
                        >
                          <i class="bi bi-arrow-counterclockwise me-1"></i>重置默认
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import apiService from '../services/api'
import VoiceService from '../services/voice'

export default {
  name: 'SettingsView',
  setup() {
    // 响应式数据
    const settings = reactive({
      voice: {
        enabled: true,
        autoSpeak: false,
        rate: 0.9,
        volume: 0.8,
        language: 'zh-CN'
      },
      ui: {
        theme: 'light',
        fontSize: 'medium',
        compactMode: false,
        showTimestamp: true
      },
      ai: {
        responseLength: 'medium',
        enableContext: true,
        enableMCP: true,
        maxTokens: 500
      }
    })

    const systemInfo = reactive({
      version: '2.0.0',
      apiUrl: '',
      connected: false,
      latency: 0,
      browser: '',
      voiceSupport: false
    })

    const isSaving = ref(false)
    const isChecking = ref(false)
    const isTesting = ref(false)

    // 语音服务
    const voiceService = new VoiceService()

    // 方法
    const loadSettings = () => {
      try {
        const saved = localStorage.getItem('tcm_settings')
        if (saved) {
          const parsed = JSON.parse(saved)
          Object.assign(settings, parsed)
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    }

    const saveSettings = () => {
      try {
        isSaving.value = true
        localStorage.setItem('tcm_settings', JSON.stringify(settings))
        
        // 应用语音设置
        voiceService.setConfig({
          synthesis: {
            rate: settings.voice.rate,
            volume: settings.voice.volume,
            language: settings.voice.language
          }
        })
        
        window.showNotification('success', '设置保存成功')
      } catch (error) {
        console.error('保存设置失败:', error)
        window.showNotification('error', '设置保存失败')
      } finally {
        isSaving.value = false
      }
    }

    const resetSettings = () => {
      if (confirm('确定要重置所有设置为默认值吗？')) {
        // 重置为默认值
        settings.voice.enabled = true
        settings.voice.autoSpeak = false
        settings.voice.rate = 0.9
        settings.voice.volume = 0.8
        settings.voice.language = 'zh-CN'
        
        settings.ui.theme = 'light'
        settings.ui.fontSize = 'medium'
        settings.ui.compactMode = false
        settings.ui.showTimestamp = true
        
        settings.ai.responseLength = 'medium'
        settings.ai.enableContext = true
        settings.ai.enableMCP = true
        settings.ai.maxTokens = 500
        
        saveSettings()
        window.showNotification('info', '设置已重置为默认值')
      }
    }

    const testVoice = async () => {
      try {
        isTesting.value = true
        await voiceService.speak('这是语音测试，家庭私人医生小帮手为您服务')
        window.showNotification('success', '语音测试完成')
      } catch (error) {
        console.error('语音测试失败:', error)
        window.showNotification('error', '语音测试失败')
      } finally {
        isTesting.value = false
      }
    }

    const checkConnection = async () => {
      try {
        isChecking.value = true
        const result = await apiService.testConnection()
        
        systemInfo.connected = result.connected
        systemInfo.latency = result.latency
        
        if (result.connected) {
          window.showNotification('success', '连接正常')
        } else {
          window.showNotification('error', `连接失败: ${result.error}`)
        }
      } catch (error) {
        console.error('连接检查失败:', error)
        window.showNotification('error', '连接检查失败')
      } finally {
        isChecking.value = false
      }
    }

    const exportSettings = () => {
      const data = {
        export_time: new Date().toISOString(),
        version: systemInfo.version,
        settings: settings
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `tcm_settings_${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      window.showNotification('success', '设置导出成功')
    }

    const initSystemInfo = () => {
      systemInfo.apiUrl = apiService.getApiBase()
      systemInfo.browser = navigator.userAgent.split(' ').pop()
      systemInfo.voiceSupport = voiceService.isRecognitionSupported() && voiceService.isSynthesisSupported()
    }

    // 生命周期
    onMounted(() => {
      loadSettings()
      initSystemInfo()
      checkConnection()
    })

    return {
      settings,
      systemInfo,
      isSaving,
      isChecking,
      isTesting,
      saveSettings,
      resetSettings,
      testVoice,
      checkConnection,
      exportSettings
    }
  }
}
</script>

<style scoped>
.settings-view .card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-view .card-header {
  border-bottom: 1px solid rgba(0,0,0,0.125);
}

@media (max-width: 768px) {
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    margin-bottom: 0.5rem;
  }
}
</style>

<template>
  <div class="documents-view">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <i class="bi bi-file-earmark-medical me-2"></i>
                <h5 class="mb-0">📚 文档管理</h5>
              </div>
              <button 
                class="btn btn-outline-light btn-sm"
                @click="refreshDocuments"
                :disabled="isLoading"
              >
                <i class="bi bi-arrow-clockwise me-1"></i>刷新
              </button>
            </div>
          </div>

          <div class="card-body">
            <!-- 文档上传区域 -->
            <div class="row mb-4">
              <div class="col-12">
                <div class="card border-success">
                  <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                      <i class="bi bi-cloud-upload me-2"></i>上传医学文档
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="upload-area" @drop="handleDrop" @dragover="handleDragOver" @dragleave="handleDragLeave">
                      <input 
                        ref="fileInput"
                        type="file" 
                        multiple 
                        accept=".pdf,.txt,.docx,.doc"
                        @change="handleFileSelect"
                        class="d-none"
                      >
                      
                      <div class="text-center py-4">
                        <i class="bi bi-cloud-upload display-1 text-success mb-3"></i>
                        <h5>拖拽文件到此处或点击选择</h5>
                        <p class="text-muted mb-3">
                          支持 PDF, TXT, DOCX, DOC 格式<br>
                          单个文件最大 500MB
                        </p>
                        <button 
                          class="btn btn-success"
                          @click="$refs.fileInput.click()"
                          :disabled="isUploading"
                        >
                          <i class="bi bi-folder2-open me-1"></i>选择文件
                        </button>
                      </div>
                    </div>

                    <!-- 上传进度 -->
                    <div v-if="isUploading" class="mt-3">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>正在上传...</span>
                        <span>{{ uploadProgress }}%</span>
                      </div>
                      <div class="progress">
                        <div 
                          class="progress-bar bg-success" 
                          :style="{ width: uploadProgress + '%' }"
                        ></div>
                      </div>
                    </div>

                    <!-- 选中的文件列表 -->
                    <div v-if="selectedFiles.length > 0" class="mt-3">
                      <h6>待上传文件:</h6>
                      <div class="list-group">
                        <div 
                          v-for="(file, index) in selectedFiles" 
                          :key="index"
                          class="list-group-item d-flex justify-content-between align-items-center"
                        >
                          <div>
                            <i class="bi bi-file-earmark me-2"></i>
                            {{ file.name }}
                            <small class="text-muted ms-2">({{ formatFileSize(file.size) }})</small>
                          </div>
                          <button 
                            class="btn btn-outline-danger btn-sm"
                            @click="removeSelectedFile(index)"
                          >
                            <i class="bi bi-x"></i>
                          </button>
                        </div>
                      </div>
                      
                      <div class="mt-3">
                        <button 
                          class="btn btn-success me-2"
                          @click="uploadFiles"
                          :disabled="isUploading"
                        >
                          <i class="bi bi-upload me-1"></i>开始上传
                        </button>
                        <button 
                          class="btn btn-outline-secondary"
                          @click="clearSelectedFiles"
                          :disabled="isUploading"
                        >
                          <i class="bi bi-x-circle me-1"></i>清空
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 文档列表 -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6>
                    <i class="bi bi-list-ul me-2"></i>文档库
                    <span class="badge bg-primary ms-2">{{ documents.length }}</span>
                  </h6>
                  
                  <!-- 搜索框 -->
                  <div class="input-group" style="max-width: 300px;">
                    <input 
                      v-model="searchQuery"
                      type="text" 
                      class="form-control form-control-sm"
                      placeholder="搜索文档..."
                    >
                    <button class="btn btn-outline-secondary btn-sm" type="button">
                      <i class="bi bi-search"></i>
                    </button>
                  </div>
                </div>

                <!-- 文档表格 -->
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>文档名称</th>
                        <th>类型</th>
                        <th>大小</th>
                        <th>修改时间</th>
                        <th>状态</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-if="filteredDocuments.length === 0">
                        <td colspan="6" class="text-center text-muted py-4">
                          <i class="bi bi-inbox display-1 mb-3"></i>
                          <div>{{ isLoading ? '正在加载...' : '暂无文档' }}</div>
                        </td>
                      </tr>
                      
                      <tr v-for="doc in filteredDocuments" :key="doc.name">
                        <td>
                          <div class="d-flex align-items-center">
                            <i :class="getFileIcon(doc.name)" class="me-2"></i>
                            <div>
                              <div class="fw-medium">{{ doc.name }}</div>
                              <small class="text-muted">{{ doc.path }}</small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <span :class="['badge', getTypeClass(doc.type)]">
                            {{ getTypeLabel(doc.type) }}
                          </span>
                        </td>
                        <td>{{ formatFileSize(doc.size) }}</td>
                        <td>
                          <small>{{ formatTime(doc.modified) }}</small>
                        </td>
                        <td>
                          <span class="badge bg-success">已索引</span>
                        </td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <button 
                              class="btn btn-outline-primary"
                              @click="viewDocument(doc)"
                              title="查看详情"
                            >
                              <i class="bi bi-eye"></i>
                            </button>
                            <button 
                              class="btn btn-outline-danger"
                              @click="deleteDocument(doc)"
                              title="删除文档"
                            >
                              <i class="bi bi-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 统计信息 -->
            <div class="row mt-4">
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-files display-4 mb-2"></i>
                    <h5>{{ documents.length }}</h5>
                    <small>总文档数</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-database display-4 mb-2"></i>
                    <h5>{{ vectorDbChunks }}</h5>
                    <small>向量块数</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-hdd display-4 mb-2"></i>
                    <h5>{{ formatFileSize(totalSize) }}</h5>
                    <small>总大小</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                  <div class="card-body text-center">
                    <i class="bi bi-clock display-4 mb-2"></i>
                    <h5>{{ formatTime(lastUpdate) }}</h5>
                    <small>最后更新</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import apiService from '../services/api'

export default {
  name: 'DocumentsView',
  setup() {
    // 响应式数据
    const documents = ref([])
    const selectedFiles = ref([])
    const isLoading = ref(false)
    const isUploading = ref(false)
    const uploadProgress = ref(0)
    const searchQuery = ref('')
    const vectorDbChunks = ref(0)

    // 计算属性
    const filteredDocuments = computed(() => {
      if (!searchQuery.value) return documents.value
      
      const query = searchQuery.value.toLowerCase()
      return documents.value.filter(doc => 
        doc.name.toLowerCase().includes(query) ||
        doc.path.toLowerCase().includes(query)
      )
    })

    const totalSize = computed(() => {
      return documents.value.reduce((sum, doc) => sum + doc.size, 0)
    })

    const lastUpdate = computed(() => {
      if (documents.value.length === 0) return new Date().toISOString()
      
      const latest = documents.value.reduce((latest, doc) => {
        return new Date(doc.modified) > new Date(latest) ? doc.modified : latest
      }, documents.value[0].modified)
      
      return latest
    })

    // 方法
    const formatFileSize = (bytes) => {
      return window.formatFileSize(bytes)
    }

    const formatTime = (timestamp) => {
      return window.formatTime(timestamp)
    }

    const getFileIcon = (filename) => {
      const ext = filename.split('.').pop().toLowerCase()
      const icons = {
        pdf: 'bi-file-earmark-pdf text-danger',
        txt: 'bi-file-earmark-text text-primary',
        docx: 'bi-file-earmark-word text-primary',
        doc: 'bi-file-earmark-word text-primary'
      }
      return icons[ext] || 'bi-file-earmark text-secondary'
    }

    const getTypeClass = (type) => {
      return type === 'system' ? 'bg-success' : 'bg-primary'
    }

    const getTypeLabel = (type) => {
      return type === 'system' ? '系统文档' : '用户上传'
    }

    const loadDocuments = async () => {
      try {
        isLoading.value = true
        const response = await apiService.getDocuments()
        documents.value = response.data.documents || []
        vectorDbChunks.value = response.data.vector_db_chunks || 0
      } catch (error) {
        console.error('加载文档列表失败:', error)
        window.showNotification('error', `加载文档失败: ${error.message}`)
      } finally {
        isLoading.value = false
      }
    }

    const refreshDocuments = () => {
      loadDocuments()
    }

    const handleFileSelect = (event) => {
      const files = Array.from(event.target.files)
      addSelectedFiles(files)
    }

    const handleDrop = (event) => {
      event.preventDefault()
      const files = Array.from(event.dataTransfer.files)
      addSelectedFiles(files)
    }

    const handleDragOver = (event) => {
      event.preventDefault()
    }

    const handleDragLeave = (event) => {
      event.preventDefault()
    }

    const addSelectedFiles = (files) => {
      const validFiles = files.filter(file => {
        const validTypes = ['.pdf', '.txt', '.docx', '.doc']
        const ext = '.' + file.name.split('.').pop().toLowerCase()
        return validTypes.includes(ext) && file.size <= 500 * 1024 * 1024
      })

      if (validFiles.length !== files.length) {
        window.showNotification('warning', '部分文件格式不支持或文件过大')
      }

      selectedFiles.value.push(...validFiles)
    }

    const removeSelectedFile = (index) => {
      selectedFiles.value.splice(index, 1)
    }

    const clearSelectedFiles = () => {
      selectedFiles.value = []
    }

    const uploadFiles = async () => {
      if (selectedFiles.value.length === 0) return

      try {
        isUploading.value = true
        uploadProgress.value = 0

        const response = await apiService.uploadDocuments(
          selectedFiles.value,
          (progress) => {
            uploadProgress.value = progress
          }
        )

        const result = response.data
        
        window.showNotification('success', 
          `上传完成: ${result.successful_uploads}个成功, ${result.failed_uploads}个失败`
        )

        if (result.errors.length > 0) {
          console.error('上传错误:', result.errors)
        }

        // 清空选中文件并刷新列表
        clearSelectedFiles()
        await loadDocuments()

      } catch (error) {
        console.error('文档上传失败:', error)
        window.showNotification('error', `上传失败: ${error.message}`)
      } finally {
        isUploading.value = false
        uploadProgress.value = 0
      }
    }

    const viewDocument = (doc) => {
      // 这里可以实现文档预览功能
      window.showNotification('info', `查看文档: ${doc.name}`)
    }

    const deleteDocument = (doc) => {
      if (confirm(`确定要删除文档 "${doc.name}" 吗？`)) {
        // 这里可以实现删除功能
        window.showNotification('info', `删除文档: ${doc.name}`)
      }
    }

    // 生命周期
    onMounted(() => {
      loadDocuments()
    })

    return {
      documents,
      selectedFiles,
      isLoading,
      isUploading,
      uploadProgress,
      searchQuery,
      vectorDbChunks,
      filteredDocuments,
      totalSize,
      lastUpdate,
      formatFileSize,
      formatTime,
      getFileIcon,
      getTypeClass,
      getTypeLabel,
      refreshDocuments,
      handleFileSelect,
      handleDrop,
      handleDragOver,
      handleDragLeave,
      removeSelectedFile,
      clearSelectedFiles,
      uploadFiles,
      viewDocument,
      deleteDocument
    }
  }
}
</script>

<style scoped>
.upload-area {
  border: 2px dashed #28a745;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #20c997;
  background-color: #f8f9fa;
}

.upload-area.dragover {
  border-color: #20c997;
  background-color: #e8f5e8;
}

@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.9rem;
  }
  
  .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
  }
}
</style>

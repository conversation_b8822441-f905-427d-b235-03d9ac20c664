#!/usr/bin/env python3
"""
测试MCP服务功能
"""

import time
import requests
import json

def test_mcp_service():
    print('⏳ 等待MCP服务启动...')
    
    # 等待服务启动
    for i in range(10):
        try:
            response = requests.get('http://localhost:8006/health', timeout=2)
            if response.status_code == 200:
                print('✅ MCP服务启动成功')
                print(f'服务信息: {response.json()}')
                break
        except Exception as e:
            pass
        time.sleep(1)
        print(f'   尝试 {i+1}/10...')
    else:
        print('❌ MCP服务启动超时')
        return False
    
    # 测试知识检索功能
    test_queries = [
        '肾虚怎么治疗',
        '脾虚的症状',
        '湿气重如何调理',
        '失眠多梦',
        '四君子汤'
    ]
    
    print('\n🧪 测试知识检索功能...')
    
    for query in test_queries:
        print(f'\n🔍 测试查询: {query}')
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "max_results": 2,
                    "domain": "medical"
                },
                "id": "test_request"
            }
            
            response = requests.post(
                'http://localhost:8006/mcp',
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and result['result'].get('results'):
                    results = result['result']['results']
                    print(f'   ✅ 返回 {len(results)} 个结果')
                    
                    for i, res in enumerate(results, 1):
                        title = res.get('title', '无标题')
                        score = res.get('score', 0)
                        content = res.get('content', '')[:100]
                        print(f'   结果{i}: {title} (分数: {score})')
                        print(f'        内容: {content}...')
                else:
                    print('   ❌ 无返回结果')
            else:
                print(f'   ❌ HTTP错误: {response.status_code}')
                
        except Exception as e:
            print(f'   ❌ 请求失败: {e}')
    
    return True

if __name__ == "__main__":
    test_mcp_service()

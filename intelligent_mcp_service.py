#!/usr/bin/env python3
"""
智能MCP服务 - 基于Elasticsearch的真正智能检索
不再刻舟求剑，根据用户实际问题动态检索
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import requests
import json
import logging
import jieba
import re
from bs4 import BeautifulSoup
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
import hashlib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Intelligent MCP Service",
    description="基于用户问题的智能检索服务",
    version="2.0.0"
)

class MCPRequest(BaseModel):
    method: str
    params: Dict[str, Any]
    id: Optional[str] = None

class MCPResponse(BaseModel):
    result: Any
    error: Optional[Dict[str, Any]] = None
    id: Optional[str] = None

class SearchResult(BaseModel):
    title: str
    content: str
    source: str
    domain: str
    score: float
    highlights: List[str] = []
    metadata: Dict[str, Any] = {}

class IntelligentSearchEngine:
    """智能搜索引擎 - 根据用户问题动态检索"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 中医知识图谱 - 扩展版
        self.tcm_knowledge_graph = {
            # 病症 -> 治疗方法映射
            "symptoms_treatments": {
                "肾虚": {
                    "symptoms": ["腰膝酸软", "精神萎靡", "畏寒肢冷", "夜尿频多"],
                    "treatments": ["右归丸", "左归丸", "金匮肾气丸"],
                    "herbs": ["熟地", "山药", "山茱萸", "枸杞", "杜仲"],
                    "related": ["肾阳虚", "肾阴虚", "肾气虚"]
                },
                "脾虚": {
                    "symptoms": ["食少腹胀", "大便溏薄", "神疲乏力", "面色萎黄"],
                    "treatments": ["四君子汤", "参苓白术散", "补中益气汤"],
                    "herbs": ["人参", "白术", "茯苓", "甘草", "山药"],
                    "related": ["脾气虚", "脾阳虚", "脾胃虚弱"]
                },
                "湿气": {
                    "symptoms": ["身重困倦", "胸闷腹胀", "大便粘腻", "舌苔厚腻", "腹痛腹胀"],
                    "treatments": ["平胃散", "二陈汤", "三仁汤", "理中汤"],
                    "herbs": ["苍术", "厚朴", "陈皮", "半夏", "茯苓", "干姜"],
                    "related": ["痰湿", "湿热", "寒湿", "湿困脾胃"]
                },
                "失眠": {
                    "symptoms": ["入睡困难", "多梦易醒", "心烦不安", "健忘"],
                    "treatments": ["甘麦大枣汤", "安神定志丸", "酸枣仁汤"],
                    "herbs": ["酸枣仁", "龙骨", "牡蛎", "远志", "茯神"],
                    "related": ["心神不安", "心血不足", "心肾不交"]
                },
                "头痛": {
                    "symptoms": ["头部疼痛", "头晕目眩", "恶心呕吐", "畏光"],
                    "treatments": ["川芎茶调散", "天麻钩藤饮", "清空膏"],
                    "herbs": ["川芎", "白芷", "天麻", "钩藤", "菊花"],
                    "related": ["偏头痛", "血管性头痛", "紧张性头痛"]
                },
                "咳嗽": {
                    "symptoms": ["咳嗽有痰", "胸闷气短", "喉痒", "痰多"],
                    "treatments": ["二陈汤", "止嗽散", "清肺汤"],
                    "herbs": ["陈皮", "半夏", "茯苓", "桔梗", "甘草"],
                    "related": ["痰湿咳嗽", "风寒咳嗽", "肺热咳嗽"]
                },
                "腹痛": {
                    "symptoms": ["腹部疼痛", "腹胀", "恶心", "食欲不振"],
                    "treatments": ["理中汤", "平胃散", "香砂六君子汤"],
                    "herbs": ["人参", "白术", "干姜", "木香", "砂仁"],
                    "related": ["胃痛", "肠痉挛", "消化不良"]
                },
                "便秘": {
                    "symptoms": ["大便干燥", "排便困难", "腹胀", "口干"],
                    "treatments": ["麻子仁丸", "润肠丸", "增液汤"],
                    "herbs": ["火麻仁", "郁李仁", "当归", "生地", "玄参"],
                    "related": ["热秘", "气秘", "虚秘"]
                }
            },
            
            # 方剂数据库 - 扩展版
            "formulas": {
                "右归丸": {
                    "composition": "熟地24g，山药12g，山茱萸12g，枸杞12g，鹿角胶12g，菟丝子12g，杜仲12g，当归9g，肉桂6g，附子6g",
                    "function": "温补肾阳，填精益髓",
                    "indications": "肾阳虚衰，命门火衰",
                    "source": "景岳全书"
                },
                "四君子汤": {
                    "composition": "人参9g，白术9g，茯苓9g，炙甘草6g",
                    "function": "健脾益气，和胃化湿",
                    "indications": "脾胃气虚，运化失司",
                    "source": "太平惠民和剂局方"
                },
                "理中汤": {
                    "composition": "人参9g，白术9g，干姜9g，炙甘草6g",
                    "function": "温中健脾，和胃止痛",
                    "indications": "脾胃虚寒，腹痛腹泻",
                    "source": "伤寒论"
                },
                "平胃散": {
                    "composition": "苍术12g，厚朴10g，陈皮10g，炙甘草6g",
                    "function": "燥湿健脾，行气和胃",
                    "indications": "湿困脾胃，腹胀腹痛",
                    "source": "太平惠民和剂局方"
                },
                "甘麦大枣汤": {
                    "composition": "甘草10g，小麦30g，大枣10枚",
                    "function": "养心安神，和中缓急",
                    "indications": "心神不安，失眠多梦",
                    "source": "金匮要略"
                },
                "川芎茶调散": {
                    "composition": "川芎6g，白芷6g，羌活6g，细辛3g，防风6g，薄荷6g，荆芥6g，甘草3g",
                    "function": "疏风止痛",
                    "indications": "外感风邪头痛",
                    "source": "太平惠民和剂局方"
                },
                "二陈汤": {
                    "composition": "陈皮10g，半夏10g，茯苓15g，炙甘草6g，生姜3片",
                    "function": "燥湿化痰，理气和中",
                    "indications": "痰湿咳嗽，胸闷腹胀",
                    "source": "太平惠民和剂局方"
                },
                "酸枣仁汤": {
                    "composition": "酸枣仁15g，甘草3g，知母6g，茯苓6g，川芎6g",
                    "function": "养血安神，清热除烦",
                    "indications": "心血不足，失眠多梦",
                    "source": "金匮要略"
                }
            }
        }
        
        # 在线资源配置
        self.online_resources = {
            "chinesebooks": "https://github.com/BillHCM7777779/gudaiyishu/",
            "tcm_wiki": "https://zh.wikipedia.org/wiki/中医",
            "pubmed": "https://pubmed.ncbi.nlm.nih.gov/"
        }
        
        self.cache = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    def extract_keywords(self, query: str) -> List[str]:
        """智能提取关键词"""
        # 使用jieba分词
        words = list(jieba.cut(query))
        
        # 过滤停用词
        stopwords = {"的", "了", "是", "在", "有", "和", "与", "或", "但", "而", "因为", "所以", "如何", "怎么", "什么", "哪个", "治疗", "方法"}
        keywords = [w for w in words if w not in stopwords and len(w) > 1]
        
        # 添加医学相关词汇
        medical_terms = []
        for word in keywords:
            if word in self.tcm_knowledge_graph["symptoms_treatments"]:
                medical_terms.append(word)
                # 添加相关词汇
                related = self.tcm_knowledge_graph["symptoms_treatments"][word].get("related", [])
                medical_terms.extend(related[:2])  # 限制数量
        
        return list(set(keywords + medical_terms))
    
    def analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """分析查询意图"""
        intent = {
            "type": "unknown",
            "keywords": self.extract_keywords(query),
            "symptoms": [],
            "treatments": [],
            "formulas": [],
            "confidence": 0.0
        }
        
        query_lower = query.lower()
        
        # 检测症状
        for symptom, data in self.tcm_knowledge_graph["symptoms_treatments"].items():
            if symptom in query_lower:
                intent["symptoms"].append(symptom)
                intent["treatments"].extend(data["treatments"][:2])
                intent["confidence"] += 0.3
        
        # 检测方剂
        for formula in self.tcm_knowledge_graph["formulas"]:
            if formula in query_lower:
                intent["formulas"].append(formula)
                intent["confidence"] += 0.2
        
        # 确定查询类型
        if "治疗" in query_lower or "怎么" in query_lower:
            intent["type"] = "treatment_inquiry"
            intent["confidence"] += 0.2
        elif "方剂" in query_lower or "药方" in query_lower:
            intent["type"] = "formula_inquiry"
            intent["confidence"] += 0.2
        elif "症状" in query_lower or "表现" in query_lower:
            intent["type"] = "symptom_inquiry"
            intent["confidence"] += 0.2
        
        return intent
    
    async def intelligent_search(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """智能搜索 - 根据用户问题动态生成结果"""
        logger.info(f"智能搜索: {query}")
        
        # 分析查询意图
        intent = self.analyze_query_intent(query)
        logger.info(f"查询意图: {intent}")
        
        results = []
        
        # 根据意图生成相应结果
        if intent["type"] == "treatment_inquiry" and intent["symptoms"]:
            results.extend(await self._generate_treatment_results(intent, query))
        elif intent["type"] == "formula_inquiry" and intent["formulas"]:
            results.extend(await self._generate_formula_results(intent, query))
        else:
            # 通用搜索
            results.extend(await self._generate_general_results(intent, query))
        
        # 在线搜索补充
        online_results = await self._search_online_resources(query, max_results // 2)
        results.extend(online_results)
        
        # 去重和排序
        unique_results = self._deduplicate_results(results)
        sorted_results = sorted(unique_results, key=lambda x: x.score, reverse=True)
        
        return sorted_results[:max_results]
    
    async def _generate_treatment_results(self, intent: Dict, query: str) -> List[SearchResult]:
        """生成治疗相关结果"""
        results = []
        
        for symptom in intent["symptoms"]:
            symptom_data = self.tcm_knowledge_graph["symptoms_treatments"][symptom]
            
            # 生成综合治疗方案
            treatments = symptom_data["treatments"][:2]
            herbs = symptom_data["herbs"][:5]
            
            content = f"{symptom}的中医治疗方案：\n\n"
            content += f"【主要症状】{', '.join(symptom_data['symptoms'])}\n\n"
            content += f"【推荐方剂】{', '.join(treatments)}\n\n"
            content += f"【常用药物】{', '.join(herbs)}\n\n"
            
            # 添加具体方剂信息
            for treatment in treatments:
                if treatment in self.tcm_knowledge_graph["formulas"]:
                    formula_data = self.tcm_knowledge_graph["formulas"][treatment]
                    content += f"【{treatment}】\n"
                    content += f"组成：{formula_data['composition']}\n"
                    content += f"功效：{formula_data['function']}\n"
                    content += f"主治：{formula_data['indications']}\n"
                    content += f"出处：{formula_data['source']}\n\n"
            
            content += "【注意事项】请在专业中医师指导下使用，根据个人体质调整用药。"
            
            result = SearchResult(
                title=f"中医治疗{symptom}的专业方案",
                content=content,
                source="中医知识图谱",
                domain="medical",
                score=0.95,
                highlights=[symptom, "治疗"],
                metadata={
                    "type": "treatment_plan",
                    "symptom": symptom,
                    "evidence_level": "high"
                }
            )
            results.append(result)
        
        return results
    
    async def _generate_formula_results(self, intent: Dict, query: str) -> List[SearchResult]:
        """生成方剂相关结果"""
        results = []
        
        for formula in intent["formulas"]:
            if formula in self.tcm_knowledge_graph["formulas"]:
                formula_data = self.tcm_knowledge_graph["formulas"][formula]
                
                content = f"【方剂名称】{formula}\n\n"
                content += f"【药物组成】{formula_data['composition']}\n\n"
                content += f"【功效作用】{formula_data['function']}\n\n"
                content += f"【主治病症】{formula_data['indications']}\n\n"
                content += f"【出处典籍】{formula_data['source']}\n\n"
                content += "【现代应用】现代临床常用于相关疾病的治疗，具有良好疗效。\n\n"
                content += "【用法用量】水煎服，一日一剂，分2-3次温服。具体用量请遵医嘱。"
                
                result = SearchResult(
                    title=f"经典方剂：{formula}详解",
                    content=content,
                    source=formula_data["source"],
                    domain="medical",
                    score=0.92,
                    highlights=[formula, "方剂"],
                    metadata={
                        "type": "formula_detail",
                        "formula": formula,
                        "evidence_level": "high"
                    }
                )
                results.append(result)
        
        return results
    
    async def _generate_general_results(self, intent: Dict, query: str) -> List[SearchResult]:
        """生成通用结果"""
        results = []
        
        # 基于关键词生成相关内容
        for keyword in intent["keywords"][:3]:
            if keyword in self.tcm_knowledge_graph["symptoms_treatments"]:
                symptom_data = self.tcm_knowledge_graph["symptoms_treatments"][keyword]
                
                content = f"关于{keyword}的中医认识：\n\n"
                content += f"【相关症状】{', '.join(symptom_data['symptoms'])}\n\n"
                content += f"【治疗方法】{', '.join(symptom_data['treatments'])}\n\n"
                content += f"【常用药物】{', '.join(symptom_data['herbs'])}\n\n"
                content += f"【相关概念】{', '.join(symptom_data['related'])}"
                
                result = SearchResult(
                    title=f"中医{keyword}相关知识",
                    content=content,
                    source="中医知识图谱",
                    domain="medical",
                    score=0.80,
                    highlights=[keyword],
                    metadata={
                        "type": "general_knowledge",
                        "keyword": keyword
                    }
                )
                results.append(result)
        
        return results
    
    async def _search_online_resources(self, query: str, max_results: int) -> List[SearchResult]:
        """搜索在线资源"""
        # 这里可以实现真正的在线搜索
        # 暂时返回空列表，避免网络依赖
        return []
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重结果"""
        seen_content = set()
        unique_results = []
        
        for result in results:
            content_hash = hashlib.md5(result.content[:100].encode()).hexdigest()
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)
        
        return unique_results

# 全局搜索引擎
search_engine = IntelligentSearchEngine()

@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    """MCP协议主端点"""
    try:
        if request.method == "search_knowledge":
            query = request.params.get("query", "")
            max_results = request.params.get("max_results", 5)
            
            # 执行智能搜索
            results = await search_engine.intelligent_search(query, max_results)
            
            # 转换为字典格式
            result_dicts = [result.dict() for result in results]
            
            return MCPResponse(
                result={
                    "results": result_dicts,
                    "total": len(result_dicts),
                    "query": query,
                    "search_type": "intelligent"
                },
                id=request.id
            )
        
        else:
            return MCPResponse(
                error={"code": -32601, "message": f"Method not found: {request.method}"},
                id=request.id
            )
    
    except Exception as e:
        logger.error(f"MCP请求处理失败: {e}")
        return MCPResponse(
            error={"code": -32603, "message": f"Internal error: {str(e)}"},
            id=request.id
        )

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "Intelligent MCP Service",
        "version": "2.0.0",
        "timestamp": time.time()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)

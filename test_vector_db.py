#!/usr/bin/env python3
"""
测试向量数据库功能
"""

import sys
sys.path.append('.')

try:
    from intelligent_rag_retriever import IntelligentRAGRetriever
    
    print('🔄 重新初始化智能RAG检索器...')
    retriever = IntelligentRAGRetriever()
    
    if retriever.initialize():
        print('✅ RAG检索器初始化成功')
        
        # 获取统计信息
        stats = retriever.get_retrieval_stats()
        print('📊 统计信息:')
        print(f'   - 文档块数量: {stats["total_chunks"]}')
        print(f'   - 向量索引大小: {stats["vector_index_size"]}')
        print(f'   - TF-IDF特征数: {stats["tfidf_features"]}')
        
        # 测试检索功能
        test_queries = [
            '栀子甘草豉汤',
            '失眠多梦',
            '脾胃虚弱'
        ]
        
        for query in test_queries:
            print(f'\n🔍 测试查询: {query}')
            results = retriever.search(query, top_k=2)
            
            if results:
                for i, result in enumerate(results, 1):
                    content = result.get('content', '')[:100]
                    score = result.get('combined_score', result.get('score', 0))
                    methods = result.get('methods', ['unknown'])
                    print(f'   结果{i}: 分数={score:.3f}, 方法={methods}')
                    print(f'        内容={content}...')
            else:
                print('   ❌ 无检索结果')
    else:
        print('❌ RAG检索器初始化失败')
        
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()

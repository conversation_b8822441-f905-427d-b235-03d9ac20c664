#!/usr/bin/env python3
"""
🧪 Vue.js + Flask 架构集成测试
测试新架构的所有核心功能
"""

import requests
import json
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VueFlaskIntegrationTest:
    def __init__(self):
        self.flask_base = 'http://localhost:5000'
        self.vue_base = 'http://localhost:3000'
        self.mcp_base = 'http://localhost:8006'
        
        self.test_results = {
            'flask_backend': False,
            'mcp_service': False,
            'vue_frontend': False,
            'api_endpoints': {},
            'integration_flow': False
        }
    
    def test_flask_backend(self):
        """测试Flask后端服务"""
        logger.info("🧪 测试Flask后端服务...")
        
        try:
            # 测试健康检查
            response = requests.get(f'{self.flask_base}/api/health', timeout=10)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Flask后端健康: {data.get('status')}")
                logger.info(f"📊 向量数据库块数: {data.get('vector_db_chunks', 0)}")
                self.test_results['flask_backend'] = True
                return True
            else:
                logger.error(f"❌ Flask后端响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Flask后端连接失败: {e}")
            return False
    
    def test_mcp_service(self):
        """测试MCP智能服务"""
        logger.info("🧪 测试MCP智能服务...")
        
        try:
            # 测试健康检查
            response = requests.get(f'{self.mcp_base}/health', timeout=10)
            if response.status_code == 200:
                logger.info("✅ MCP服务健康检查通过")
                
                # 测试智能搜索
                search_data = {
                    "method": "search_knowledge",
                    "params": {
                        "query": "肾虚怎么治疗",
                        "max_results": 3
                    },
                    "id": "test"
                }
                
                response = requests.post(
                    f'{self.mcp_base}/mcp',
                    json=search_data,
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"✅ MCP智能搜索成功: {len(result.get('result', {}).get('results', []))} 个结果")
                    self.test_results['mcp_service'] = True
                    return True
                else:
                    logger.error(f"❌ MCP搜索失败: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ MCP服务连接失败: {e}")
            return False
    
    def test_vue_frontend(self):
        """测试Vue.js前端服务"""
        logger.info("🧪 测试Vue.js前端服务...")
        
        try:
            response = requests.get(self.vue_base, timeout=10)
            if response.status_code == 200:
                content = response.text
                if 'Vue.js' in content or 'app' in content:
                    logger.info("✅ Vue.js前端服务正常")
                    self.test_results['vue_frontend'] = True
                    return True
                else:
                    logger.warning("⚠️ Vue.js前端内容异常")
                    return False
            else:
                logger.error(f"❌ Vue.js前端响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Vue.js前端连接失败: {e}")
            return False
    
    def test_api_endpoints(self):
        """测试所有API端点"""
        logger.info("🧪 测试API端点...")
        
        endpoints = {
            '/api/health': 'GET',
            '/api/sessions': 'GET',
            '/api/documents': 'GET'
        }
        
        for endpoint, method in endpoints.items():
            try:
                if method == 'GET':
                    response = requests.get(f'{self.flask_base}{endpoint}', timeout=10)
                else:
                    response = requests.post(f'{self.flask_base}{endpoint}', timeout=10)
                
                if response.status_code in [200, 201]:
                    logger.info(f"✅ {endpoint} 端点正常")
                    self.test_results['api_endpoints'][endpoint] = True
                else:
                    logger.warning(f"⚠️ {endpoint} 端点响应: {response.status_code}")
                    self.test_results['api_endpoints'][endpoint] = False
                    
            except Exception as e:
                logger.error(f"❌ {endpoint} 端点测试失败: {e}")
                self.test_results['api_endpoints'][endpoint] = False
    
    def test_chat_integration(self):
        """测试聊天集成流程"""
        logger.info("🧪 测试聊天集成流程...")
        
        try:
            # 创建新会话
            response = requests.post(f'{self.flask_base}/api/sessions', timeout=10)
            if response.status_code == 201:
                session_data = response.json()
                session_id = session_data['session_id']
                logger.info(f"✅ 创建会话成功: {session_id}")
                
                # 发送测试消息
                chat_data = {
                    "message": "栀子甘草豉汤方的组成是什么？",
                    "session_id": session_id
                }
                
                logger.info("📤 发送测试消息...")
                response = requests.post(
                    f'{self.flask_base}/api/chat',
                    json=chat_data,
                    timeout=60  # AI响应可能需要更长时间
                )
                
                if response.status_code == 200:
                    result = response.json()
                    response_text = result.get('response', '')
                    response_time = result.get('response_time', 0)
                    
                    logger.info(f"✅ 聊天响应成功")
                    logger.info(f"📝 响应长度: {len(response_text)} 字符")
                    logger.info(f"⏱️ 响应时间: {response_time:.2f} 秒")
                    logger.info(f"📄 响应预览: {response_text[:100]}...")
                    
                    # 检查响应质量
                    if len(response_text) > 50 and '栀子' in response_text:
                        logger.info("✅ 响应内容质量良好")
                        self.test_results['integration_flow'] = True
                        return True
                    else:
                        logger.warning("⚠️ 响应内容质量待改进")
                        return False
                else:
                    logger.error(f"❌ 聊天请求失败: {response.status_code}")
                    return False
            else:
                logger.error(f"❌ 创建会话失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 聊天集成测试失败: {e}")
            return False
    
    def test_voice_endpoints(self):
        """测试语音功能端点"""
        logger.info("🧪 测试语音功能端点...")
        
        try:
            # 测试文本转语音
            tts_data = {"text": "这是语音测试"}
            response = requests.post(
                f'{self.flask_base}/api/voice/speak',
                json=tts_data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info("✅ 文本转语音端点正常")
                return True
            else:
                logger.warning(f"⚠️ 文本转语音端点响应: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 语音端点测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始Vue.js + Flask架构集成测试...")
        logger.info("="*60)
        
        # 等待服务启动
        logger.info("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 执行测试
        tests = [
            ('Flask后端服务', self.test_flask_backend),
            ('MCP智能服务', self.test_mcp_service),
            ('Vue.js前端服务', self.test_vue_frontend),
            ('API端点', self.test_api_endpoints),
            ('语音功能', self.test_voice_endpoints),
            ('聊天集成流程', self.test_chat_integration)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 执行测试: {test_name}")
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
        
        # 生成测试报告
        self.generate_report(passed, total)
    
    def generate_report(self, passed, total):
        """生成测试报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 Vue.js + Flask 架构集成测试报告")
        logger.info("="*60)
        
        success_rate = (passed / total) * 100
        logger.info(f"📈 测试通过率: {success_rate:.1f}% ({passed}/{total})")
        
        logger.info("\n🔍 详细结果:")
        logger.info(f"  • Flask后端服务: {'✅' if self.test_results['flask_backend'] else '❌'}")
        logger.info(f"  • MCP智能服务: {'✅' if self.test_results['mcp_service'] else '❌'}")
        logger.info(f"  • Vue.js前端服务: {'✅' if self.test_results['vue_frontend'] else '❌'}")
        logger.info(f"  • 聊天集成流程: {'✅' if self.test_results['integration_flow'] else '❌'}")
        
        logger.info(f"\n🌐 API端点测试:")
        for endpoint, result in self.test_results['api_endpoints'].items():
            logger.info(f"  • {endpoint}: {'✅' if result else '❌'}")
        
        if success_rate >= 80:
            logger.info("\n🎉 系统集成测试总体通过！")
            logger.info("✅ Vue.js + Flask架构运行正常")
        else:
            logger.warning("\n⚠️ 系统集成测试存在问题")
            logger.warning("❌ 需要检查失败的组件")
        
        logger.info("\n🔗 访问地址:")
        logger.info(f"  • 前端界面: {self.vue_base}")
        logger.info(f"  • 后端API: {self.flask_base}")
        logger.info(f"  • MCP服务: {self.mcp_base}")
        
        logger.info("="*60)

def main():
    """主函数"""
    tester = VueFlaskIntegrationTest()
    tester.run_all_tests()

if __name__ == '__main__':
    main()

2025-06-23 10:37:02,739 - INFO - 🔍 检查系统依赖...
2025-06-23 10:37:07,841 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:37:08,112 - ERROR - ❌ 缺少Python依赖: flask, flask_cors, faiss-cpu
2025-06-23 10:37:08,113 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:46:39,364 - INFO - 🔍 检查系统依赖...
2025-06-23 10:46:45,816 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:46:46,155 - ERROR - ❌ 缺少Python依赖: faiss-cpu
2025-06-23 10:46:46,155 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:47:49,803 - INFO - 🔍 检查系统依赖...
2025-06-23 10:47:55,878 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:47:56,224 - ERROR - ❌ 缺少Python依赖: faiss-cpu
2025-06-23 10:47:56,224 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:48:38,468 - INFO - 🔍 检查系统依赖...
2025-06-23 10:48:44,585 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:48:44,890 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:48:44,891 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:48:44,891 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:48:44,918 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:48:44,923 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:48:45,007 - ERROR - ❌ 未找到Node.js或npm，请先安装Node.js
2025-06-23 10:49:17,035 - INFO - 🔍 检查系统依赖...
2025-06-23 10:49:23,177 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:49:23,495 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:49:23,496 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:49:23,496 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:49:23,522 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:49:23,527 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:49:23,993 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 10:49:23,993 - INFO - ✅ npm版本: 10.9.2
2025-06-23 10:49:23,994 - INFO - 📦 安装前端依赖...
2025-06-23 10:49:23,995 - ERROR - 系统运行时发生错误: [WinError 2] 系统找不到指定的文件。
2025-06-23 10:50:38,914 - INFO - 🔍 检查系统依赖...
2025-06-23 10:50:45,016 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:50:45,328 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:50:45,328 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:50:45,328 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:50:45,359 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:50:45,365 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:50:45,830 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 10:50:45,830 - INFO - ✅ npm版本: 10.9.2
2025-06-23 10:50:45,830 - INFO - 📦 安装前端依赖...
2025-06-23 10:52:45,584 - ERROR - ❌ 前端依赖安装失败: Command '['npm', 'install']' returned non-zero exit status 1.
2025-06-23 11:02:17,081 - INFO - 🔍 检查系统依赖...
2025-06-23 11:02:22,163 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 11:02:22,450 - INFO - Loading faiss with AVX512 support.
2025-06-23 11:02:22,450 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 11:02:22,450 - INFO - Loading faiss with AVX2 support.
2025-06-23 11:02:22,475 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 11:02:22,483 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 11:02:22,930 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 11:02:22,930 - INFO - ✅ npm版本: 10.9.2
2025-06-23 11:02:22,931 - INFO - ✅ 依赖检查完成
2025-06-23 11:02:22,931 - INFO - 🔌 检查端口可用性...
2025-06-23 11:02:22,935 - WARNING - ⚠️ 端口 5000 已被占用 (Flask后端服务)
2025-06-23 11:02:23,064 - INFO - 已终止占用端口 5000 的进程 (PID: 15212)
2025-06-23 11:02:23,065 - WARNING - ⚠️ 端口 8006 已被占用 (MCP智能服务)
2025-06-23 11:02:23,213 - INFO - 已终止占用端口 8006 的进程 (PID: 8024)
2025-06-23 11:02:25,247 - INFO - ✅ 端口检查完成
2025-06-23 11:02:25,248 - INFO - 🎯 开始启动所有服务...
2025-06-23 11:02:25,248 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 11:02:27,255 - INFO - ✅ MCP智能服务 启动成功 (PID: 7456)
2025-06-23 11:02:27,255 - INFO - ⏳ 等待 MCP智能服务 就绪...
2025-06-23 11:02:29,306 - INFO - ✅ MCP智能服务 就绪
2025-06-23 11:02:29,306 - INFO - 🚀 启动 Flask后端服务...
2025-06-23 11:02:32,313 - INFO - ✅ Flask后端服务 启动成功 (PID: 5976)
2025-06-23 11:02:32,313 - INFO - ⏳ 等待 Flask后端服务 就绪...
2025-06-23 11:02:44,494 - INFO - ✅ Flask后端服务 就绪
2025-06-23 11:02:44,494 - INFO - 🚀 启动 Vue.js前端服务...
2025-06-23 11:02:49,503 - ERROR - ❌ Vue.js前端服务 启动失败
2025-06-23 11:02:49,503 - ERROR - STDOUT: 
> tcm-family-doctor-frontend@2.0.0 serve
> vue-cli-service serve


2025-06-23 11:02:49,504 - ERROR - STDERR: 'vue-cli-service' 不是内部或外部命令，也不是可运行的程序
或批处理文件。

2025-06-23 11:02:49,504 - ERROR - ❌ 服务启动失败: vue_frontend

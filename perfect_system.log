2025-06-23 10:37:02,739 - INFO - 🔍 检查系统依赖...
2025-06-23 10:37:07,841 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:37:08,112 - ERROR - ❌ 缺少Python依赖: flask, flask_cors, faiss-cpu
2025-06-23 10:37:08,113 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:46:39,364 - INFO - 🔍 检查系统依赖...
2025-06-23 10:46:45,816 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:46:46,155 - ERROR - ❌ 缺少Python依赖: faiss-cpu
2025-06-23 10:46:46,155 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:47:49,803 - INFO - 🔍 检查系统依赖...
2025-06-23 10:47:55,878 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:47:56,224 - ERROR - ❌ 缺少Python依赖: faiss-cpu
2025-06-23 10:47:56,224 - INFO - 请运行: pip install -r requirements_perfect.txt
2025-06-23 10:48:38,468 - INFO - 🔍 检查系统依赖...
2025-06-23 10:48:44,585 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:48:44,890 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:48:44,891 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:48:44,891 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:48:44,918 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:48:44,923 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:48:45,007 - ERROR - ❌ 未找到Node.js或npm，请先安装Node.js
2025-06-23 10:49:17,035 - INFO - 🔍 检查系统依赖...
2025-06-23 10:49:23,177 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:49:23,495 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:49:23,496 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:49:23,496 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:49:23,522 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:49:23,527 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:49:23,993 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 10:49:23,993 - INFO - ✅ npm版本: 10.9.2
2025-06-23 10:49:23,994 - INFO - 📦 安装前端依赖...
2025-06-23 10:49:23,995 - ERROR - 系统运行时发生错误: [WinError 2] 系统找不到指定的文件。
2025-06-23 10:50:38,914 - INFO - 🔍 检查系统依赖...
2025-06-23 10:50:45,016 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 10:50:45,328 - INFO - Loading faiss with AVX512 support.
2025-06-23 10:50:45,328 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 10:50:45,328 - INFO - Loading faiss with AVX2 support.
2025-06-23 10:50:45,359 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 10:50:45,365 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 10:50:45,830 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 10:50:45,830 - INFO - ✅ npm版本: 10.9.2
2025-06-23 10:50:45,830 - INFO - 📦 安装前端依赖...
2025-06-23 10:52:45,584 - ERROR - ❌ 前端依赖安装失败: Command '['npm', 'install']' returned non-zero exit status 1.
2025-06-23 11:02:17,081 - INFO - 🔍 检查系统依赖...
2025-06-23 11:02:22,163 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 11:02:22,450 - INFO - Loading faiss with AVX512 support.
2025-06-23 11:02:22,450 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 11:02:22,450 - INFO - Loading faiss with AVX2 support.
2025-06-23 11:02:22,475 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 11:02:22,483 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 11:02:22,930 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 11:02:22,930 - INFO - ✅ npm版本: 10.9.2
2025-06-23 11:02:22,931 - INFO - ✅ 依赖检查完成
2025-06-23 11:02:22,931 - INFO - 🔌 检查端口可用性...
2025-06-23 11:02:22,935 - WARNING - ⚠️ 端口 5000 已被占用 (Flask后端服务)
2025-06-23 11:02:23,064 - INFO - 已终止占用端口 5000 的进程 (PID: 15212)
2025-06-23 11:02:23,065 - WARNING - ⚠️ 端口 8006 已被占用 (MCP智能服务)
2025-06-23 11:02:23,213 - INFO - 已终止占用端口 8006 的进程 (PID: 8024)
2025-06-23 11:02:25,247 - INFO - ✅ 端口检查完成
2025-06-23 11:02:25,248 - INFO - 🎯 开始启动所有服务...
2025-06-23 11:02:25,248 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 11:02:27,255 - INFO - ✅ MCP智能服务 启动成功 (PID: 7456)
2025-06-23 11:02:27,255 - INFO - ⏳ 等待 MCP智能服务 就绪...
2025-06-23 11:02:29,306 - INFO - ✅ MCP智能服务 就绪
2025-06-23 11:02:29,306 - INFO - 🚀 启动 Flask后端服务...
2025-06-23 11:02:32,313 - INFO - ✅ Flask后端服务 启动成功 (PID: 5976)
2025-06-23 11:02:32,313 - INFO - ⏳ 等待 Flask后端服务 就绪...
2025-06-23 11:02:44,494 - INFO - ✅ Flask后端服务 就绪
2025-06-23 11:02:44,494 - INFO - 🚀 启动 Vue.js前端服务...
2025-06-23 11:02:49,503 - ERROR - ❌ Vue.js前端服务 启动失败
2025-06-23 11:02:49,503 - ERROR - STDOUT: 
> tcm-family-doctor-frontend@2.0.0 serve
> vue-cli-service serve


2025-06-23 11:02:49,504 - ERROR - STDERR: 'vue-cli-service' 不是内部或外部命令，也不是可运行的程序
或批处理文件。

2025-06-23 11:02:49,504 - ERROR - ❌ 服务启动失败: vue_frontend
2025-06-23 11:49:30,686 - INFO - 🔍 检查系统依赖...
2025-06-23 11:49:36,780 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 11:49:37,114 - INFO - Loading faiss with AVX512 support.
2025-06-23 11:49:37,114 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 11:49:37,114 - INFO - Loading faiss with AVX2 support.
2025-06-23 11:49:37,144 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 11:49:37,152 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 11:49:37,622 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 11:49:37,622 - INFO - ✅ npm版本: 10.9.2
2025-06-23 11:49:37,622 - INFO - ✅ 依赖检查完成
2025-06-23 11:49:37,622 - INFO - 🔌 检查端口可用性...
2025-06-23 11:49:43,683 - INFO - ✅ 端口检查完成
2025-06-23 11:49:43,683 - INFO - 🎯 开始启动所有服务...
2025-06-23 11:49:43,684 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 11:49:45,694 - INFO - ✅ MCP智能服务 启动成功 (PID: 2068)
2025-06-23 11:49:45,695 - INFO - ⏳ 等待 MCP智能服务 就绪...
2025-06-23 11:49:47,730 - INFO - ✅ MCP智能服务 就绪
2025-06-23 11:49:47,730 - INFO - 🚀 启动 Flask后端服务...
2025-06-23 11:49:50,737 - INFO - ✅ Flask后端服务 启动成功 (PID: 1868)
2025-06-23 11:49:50,737 - INFO - ⏳ 等待 Flask后端服务 就绪...
2025-06-23 11:50:03,922 - INFO - ✅ Flask后端服务 就绪
2025-06-23 11:50:03,922 - INFO - 🚀 启动 Vue.js前端服务...
2025-06-23 11:50:08,932 - ERROR - ❌ Vue.js前端服务 启动失败
2025-06-23 11:50:08,932 - ERROR - STDOUT: 
> tcm-family-doctor-frontend@2.0.0 serve
> vue-cli-service serve --port 3000


2025-06-23 11:50:08,933 - ERROR - STDERR: node:internal/modules/cjs/loader:1404
  throw err;
  ^

Error: Cannot find module 'C:\Users\<USER>\Documents\augment-projects\RAG 2025\frontend\node_modules\@vue\cli-service\bin\vue-cli-service.js'
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)
    at node:internal/main/run_main_module:36:49 {
  code: 'MODULE_NOT_FOUND',
  requireStack: []
}

Node.js v22.16.0

2025-06-23 11:50:08,933 - ERROR - ❌ 服务启动失败: vue_frontend
2025-06-23 12:01:12,941 - INFO - 🔍 检查系统依赖...
2025-06-23 12:01:17,787 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 12:01:18,036 - INFO - Loading faiss with AVX512 support.
2025-06-23 12:01:18,037 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 12:01:18,037 - INFO - Loading faiss with AVX2 support.
2025-06-23 12:01:18,061 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 12:01:18,067 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 12:01:18,477 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 12:01:18,477 - INFO - ✅ npm版本: 10.9.2
2025-06-23 12:01:18,478 - INFO - ✅ 依赖检查完成
2025-06-23 12:01:18,478 - INFO - 🔌 检查端口可用性...
2025-06-23 12:01:24,543 - INFO - ✅ 端口检查完成
2025-06-23 12:01:24,543 - INFO - 🎯 开始启动所有服务...
2025-06-23 12:01:24,544 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:01:26,551 - INFO - ✅ MCP智能服务 启动成功 (PID: 4776)
2025-06-23 12:01:26,552 - INFO - ⏳ 等待 MCP智能服务 就绪...
2025-06-23 12:01:28,604 - INFO - ✅ MCP智能服务 就绪
2025-06-23 12:01:28,604 - INFO - 🚀 启动 Flask后端服务...
2025-06-23 12:01:31,611 - INFO - ✅ Flask后端服务 启动成功 (PID: 26552)
2025-06-23 12:01:31,611 - INFO - ⏳ 等待 Flask后端服务 就绪...
2025-06-23 12:01:43,715 - INFO - ✅ Flask后端服务 就绪
2025-06-23 12:01:43,715 - INFO - 🚀 启动 Vue.js前端服务...
2025-06-23 12:01:48,723 - INFO - ✅ Vue.js前端服务 启动成功 (PID: 22780)
2025-06-23 12:01:48,723 - INFO - ⏳ 等待 Vue.js前端服务 就绪...
2025-06-23 12:04:20,824 - WARNING - ⚠️ Vue.js前端服务 健康检查超时
2025-06-23 12:04:20,825 - INFO - 🎉 所有服务启动完成！
2025-06-23 12:04:28,909 - INFO - 👁️ 开始监控服务状态...
2025-06-23 12:04:30,997 - INFO - 🌐 已打开浏览器
2025-06-23 12:51:50,776 - INFO - 🔍 检查系统依赖...
2025-06-23 12:51:55,809 - INFO - PyTorch version 2.7.1+cu128 available.
2025-06-23 12:51:56,047 - INFO - Loading faiss with AVX512 support.
2025-06-23 12:51:56,047 - INFO - Could not load library with AVX512 support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_avx512'")
2025-06-23 12:51:56,047 - INFO - Loading faiss with AVX2 support.
2025-06-23 12:51:56,065 - INFO - Successfully loaded faiss with AVX2 support.
2025-06-23 12:51:56,070 - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-06-23 12:51:56,482 - INFO - ✅ Node.js版本: v22.16.0
2025-06-23 12:51:56,482 - INFO - ✅ npm版本: 10.9.2
2025-06-23 12:51:56,482 - INFO - ✅ 依赖检查完成
2025-06-23 12:51:56,482 - INFO - 🔌 检查端口可用性...
2025-06-23 12:51:56,485 - WARNING - ⚠️ 端口 5001 已被占用 (FastAPI后端服务)
2025-06-23 12:51:56,619 - INFO - 已终止占用端口 5001 的进程 (PID: 14080)
2025-06-23 12:51:56,620 - WARNING - ⚠️ 端口 8006 已被占用 (MCP智能服务)
2025-06-23 12:51:56,758 - INFO - 已终止占用端口 8006 的进程 (PID: 26836)
2025-06-23 12:51:58,806 - INFO - ✅ 端口检查完成
2025-06-23 12:51:58,806 - INFO - 🎯 开始启动所有服务...
2025-06-23 12:51:58,806 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:51:59,033 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:51:59,036 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:51:59,037 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:52:00,814 - INFO - ✅ MCP智能服务 启动成功 (PID: 2936)
2025-06-23 12:52:00,814 - INFO - ⏳ 等待 MCP智能服务 就绪...
2025-06-23 12:52:01,052 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:52:01,052 - ERROR - STDOUT: 
2025-06-23 12:52:01,052 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [16072]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:52:01,053 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:52:02,847 - INFO - ✅ MCP智能服务 就绪
2025-06-23 12:52:02,847 - INFO - 🚀 启动 FastAPI后端服务...
2025-06-23 12:52:05,855 - INFO - ✅ FastAPI后端服务 启动成功 (PID: 22068)
2025-06-23 12:52:05,855 - INFO - ⏳ 等待 FastAPI后端服务 就绪...
2025-06-23 12:52:11,054 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:52:11,055 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:52:11,055 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:52:13,061 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:52:13,061 - ERROR - STDOUT: 
2025-06-23 12:52:13,061 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [2972]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:52:13,062 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:52:18,021 - INFO - ✅ FastAPI后端服务 就绪
2025-06-23 12:52:18,021 - INFO - 🚀 启动 Vue.js前端服务...
2025-06-23 12:52:23,028 - INFO - ✅ Vue.js前端服务 启动成功 (PID: 15248)
2025-06-23 12:52:23,028 - INFO - ⏳ 等待 Vue.js前端服务 就绪...
2025-06-23 12:52:23,032 - INFO - ✅ Vue.js前端服务 就绪
2025-06-23 12:52:23,033 - INFO - 🎉 所有服务启动完成！
2025-06-23 12:52:23,062 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:52:23,063 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:52:23,063 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:52:25,069 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:52:25,069 - ERROR - STDOUT: 
2025-06-23 12:52:25,070 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [28220]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:52:25,070 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:52:27,120 - INFO - 👁️ 开始监控服务状态...
2025-06-23 12:52:29,214 - INFO - 🌐 已打开浏览器
2025-06-23 12:52:35,070 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:52:35,071 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:52:35,071 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:52:37,079 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:52:37,079 - ERROR - STDOUT: 
2025-06-23 12:52:37,079 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [25480]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:52:37,080 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:52:47,080 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:52:47,081 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:52:47,081 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:52:49,088 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:52:49,088 - ERROR - STDOUT: 
2025-06-23 12:52:49,088 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [12896]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:52:49,088 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:52:59,089 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:52:59,089 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:52:59,090 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:53:01,098 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:53:01,099 - ERROR - STDOUT: 
2025-06-23 12:53:01,099 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [25628]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:53:01,099 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:53:11,100 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:53:11,101 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:53:11,101 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:53:13,111 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:53:13,111 - ERROR - STDOUT: 
2025-06-23 12:53:13,111 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [14740]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:53:13,111 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:53:23,112 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:53:23,113 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:53:23,113 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:53:25,120 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:53:25,121 - ERROR - STDOUT: 
2025-06-23 12:53:25,121 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [18648]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:53:25,121 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:53:35,122 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:53:35,122 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:53:35,122 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:53:37,129 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:53:37,129 - ERROR - STDOUT: 
2025-06-23 12:53:37,129 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [20500]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:53:37,130 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:53:47,130 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:53:47,131 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:53:47,131 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:53:49,138 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:53:49,138 - ERROR - STDOUT: 
2025-06-23 12:53:49,139 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [7788]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:53:49,139 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:53:59,140 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:53:59,140 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:53:59,140 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:54:01,147 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:54:01,147 - ERROR - STDOUT: 
2025-06-23 12:54:01,148 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [5164]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:54:01,148 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:54:11,149 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:54:11,150 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:54:11,150 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:54:13,157 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:54:13,157 - ERROR - STDOUT: 
2025-06-23 12:54:13,157 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [5896]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:54:13,157 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:54:23,158 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:54:23,159 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:54:23,159 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:54:25,166 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:54:25,166 - ERROR - STDOUT: 
2025-06-23 12:54:25,167 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [5420]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:54:25,167 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:54:35,168 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:54:35,168 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:54:35,169 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:54:37,175 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:54:37,176 - ERROR - STDOUT: 
2025-06-23 12:54:37,176 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [11372]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:54:37,176 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:54:47,177 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:54:47,177 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:54:47,178 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:54:49,186 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:54:49,186 - ERROR - STDOUT: 
2025-06-23 12:54:49,186 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [17676]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:54:49,187 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:54:59,187 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:54:59,187 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:54:59,188 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:55:01,195 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:55:01,196 - ERROR - STDOUT: 
2025-06-23 12:55:01,196 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [27776]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:55:01,196 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:55:11,197 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:55:11,197 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:55:11,197 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:55:13,203 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:55:13,204 - ERROR - STDOUT: 
2025-06-23 12:55:13,204 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [12628]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:55:13,204 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:55:23,205 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:55:23,206 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:55:23,206 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:55:25,212 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:55:25,212 - ERROR - STDOUT: 
2025-06-23 12:55:25,212 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [14996]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:55:25,213 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:55:35,214 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:55:35,214 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:55:35,215 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:55:37,222 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:55:37,222 - ERROR - STDOUT: 
2025-06-23 12:55:37,222 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [2612]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:55:37,222 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:55:47,223 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:55:47,224 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:55:47,224 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:55:49,230 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:55:49,231 - ERROR - STDOUT: 
2025-06-23 12:55:49,231 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [15460]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:55:49,231 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:55:59,232 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:55:59,232 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:55:59,233 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:56:01,239 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:56:01,239 - ERROR - STDOUT: 
2025-06-23 12:56:01,239 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [6372]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:56:01,240 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:56:11,240 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:56:11,241 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:56:11,241 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:56:13,247 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:56:13,247 - ERROR - STDOUT: 
2025-06-23 12:56:13,247 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [25788]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:56:13,248 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:56:23,248 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:56:23,249 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:56:23,249 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:56:25,255 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:56:25,256 - ERROR - STDOUT: 
2025-06-23 12:56:25,256 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [15300]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:56:25,256 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:56:35,257 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:56:35,258 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:56:35,258 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:56:37,265 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:56:37,265 - ERROR - STDOUT: 
2025-06-23 12:56:37,266 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [1668]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:56:37,266 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:56:47,267 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:56:47,267 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:56:47,268 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:56:49,274 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:56:49,274 - ERROR - STDOUT: 
2025-06-23 12:56:49,274 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [20268]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:56:49,274 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:56:59,275 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:56:59,275 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:56:59,276 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:57:01,282 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:57:01,282 - ERROR - STDOUT: 
2025-06-23 12:57:01,282 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [21996]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:57:01,283 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:57:11,283 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:57:11,284 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:57:11,284 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:57:13,290 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:57:13,290 - ERROR - STDOUT: 
2025-06-23 12:57:13,291 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [27544]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:57:13,291 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:57:23,292 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:57:23,292 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:57:23,292 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:57:25,300 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:57:25,300 - ERROR - STDOUT: 
2025-06-23 12:57:25,300 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [24472]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:57:25,301 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:57:35,301 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:57:35,301 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:57:35,302 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:57:37,310 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:57:37,310 - ERROR - STDOUT: 
2025-06-23 12:57:37,310 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [19528]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:57:37,311 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:57:47,311 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:57:47,312 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:57:47,312 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:57:49,320 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:57:49,320 - ERROR - STDOUT: 
2025-06-23 12:57:49,320 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [3620]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:57:49,321 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:57:59,322 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:57:59,323 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:57:59,323 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:58:01,329 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:58:01,329 - ERROR - STDOUT: 
2025-06-23 12:58:01,329 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [27496]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:58:01,330 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:58:11,330 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:58:11,331 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:58:11,331 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:58:13,338 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:58:13,338 - ERROR - STDOUT: 
2025-06-23 12:58:13,338 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [23380]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:58:13,339 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:58:23,339 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:58:23,340 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:58:23,340 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:58:25,346 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:58:25,346 - ERROR - STDOUT: 
2025-06-23 12:58:25,346 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [25432]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:58:25,347 - ERROR - ❌ MCP智能服务 重启失败
2025-06-23 12:58:35,347 - ERROR - ❌ MCP智能服务 进程已退出
2025-06-23 12:58:35,347 - INFO - 🔄 尝试重启 MCP智能服务...
2025-06-23 12:58:35,347 - INFO - 🚀 启动 MCP智能服务...
2025-06-23 12:58:37,354 - ERROR - ❌ MCP智能服务 启动失败
2025-06-23 12:58:37,354 - ERROR - STDOUT: 
2025-06-23 12:58:37,354 - ERROR - STDERR: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\jieba\_compat.py:18: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:     Started server process [3232]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8006): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.

2025-06-23 12:58:37,354 - ERROR - ❌ MCP智能服务 重启失败

#!/usr/bin/env python3
"""
🏥 家庭私人医生小帮手 - Flask RESTful API Backend
Vue.js + Flask 架构的后端服务
保持与现有组件的完整集成：DeepSeek, RAG, MCP, Voice
"""

from flask import Flask, request, jsonify, Response, stream_with_context
from flask_cors import CORS
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import uuid
import threading
import queue

# 导入现有组件 - 保持100%兼容性
try:
    from intelligent_rag_retriever import IntelligentRAGRetriever
    RAG_AVAILABLE = True
except ImportError:
    RAG_AVAILABLE = False
    logging.error("❌ RAG检索器不可用")

try:
    from deepseek_ollama_api import DeepSeekOllamaAPI
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False
    logging.error("❌ DeepSeek模型不可用")

try:
    from intelligent_mcp_service import IntelligentSearchEngine
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logging.error("❌ MCP服务不可用")

try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    logging.error("❌ 语音功能不可用")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
CORS(app, origins=["*"])  # 允许Vue.js前端跨域访问

# 全局配置 - 保持与原系统一致
CONFIG = {
    'EMBEDDING_MODEL': './models/m3e-base',
    'VECTOR_DB_PATH': './perfect_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CONVERSATION_PATH': './conversations',
    'UPLOAD_PATH': './uploads',
    'CHUNK_SIZE': 1000,
    'TOP_K': 10,
    'MIN_RELEVANCE_SCORE': 0.3,
    'MAX_FILE_SIZE': 500 * 1024 * 1024,
    'MCP_SERVICE_URL': 'http://localhost:8006',
    'SIMILARITY_THRESHOLD': 0.35,
    'RERANK_THRESHOLD': 0.5
}

# 全局组件实例 - 严格要求100%可用性
class SystemComponents:
    def __init__(self):
        self.rag_retriever = None
        self.deepseek_api = None
        self.mcp_engine = None
        self.voice_engine = None
        self.voice_recognizer = None
        self.microphone = None
        self.initialized = False
        
    def initialize(self):
        """初始化所有组件 - 必须100%成功，不允许降级"""
        logger.info("🚀 初始化系统组件...")
        
        # RAG检索器 - 必需组件
        if not RAG_AVAILABLE:
            raise RuntimeError("❌ RAG检索器不可用，系统无法启动")
        
        self.rag_retriever = IntelligentRAGRetriever()
        if not self.rag_retriever.initialize():
            raise RuntimeError("❌ RAG检索器初始化失败，系统无法启动")
        logger.info("✅ RAG检索器初始化成功")
        
        # DeepSeek模型 - 必需组件
        if not DEEPSEEK_AVAILABLE:
            raise RuntimeError("❌ DeepSeek模型不可用，系统无法启动")
        
        self.deepseek_api = DeepSeekOllamaAPI()
        if not self.deepseek_api.available:
            raise RuntimeError("❌ DeepSeek模型不可用，系统无法启动")
        logger.info("✅ DeepSeek模型初始化成功")
        
        # MCP服务 - 必需组件
        if not MCP_AVAILABLE:
            raise RuntimeError("❌ MCP服务不可用，系统无法启动")
        
        self.mcp_engine = IntelligentSearchEngine()
        logger.info("✅ MCP服务初始化成功")
        
        # 语音功能 - 必需组件
        if not VOICE_AVAILABLE:
            raise RuntimeError("❌ 语音功能不可用，系统无法启动")
        
        try:
            self.voice_engine = pyttsx3.init()
            self.voice_engine.setProperty('rate', 150)
            self.voice_engine.setProperty('volume', 0.8)
            
            # 设置中文语音
            voices = self.voice_engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                    self.voice_engine.setProperty('voice', voice.id)
                    break
            
            self.voice_recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # 调整环境噪音
            with self.microphone as source:
                self.voice_recognizer.adjust_for_ambient_noise(source, duration=1)
            
            logger.info("✅ 语音功能初始化成功")
        except Exception as e:
            raise RuntimeError(f"❌ 语音功能初始化失败: {e}")
        
        self.initialized = True
        logger.info("🎉 所有系统组件初始化完成")

# 全局组件实例
components = SystemComponents()

# 会话管理
class SessionManager:
    def __init__(self):
        self.conversations_path = Path(CONFIG['CONVERSATION_PATH'])
        self.conversations_path.mkdir(exist_ok=True)
        self.active_sessions = {}
    
    def create_session(self) -> str:
        """创建新会话"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        self.active_sessions[session_id] = {
            'messages': [],
            'created_at': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat()
        }
        return session_id
    
    def add_message(self, session_id: str, role: str, content: str, metadata: Dict = None):
        """添加消息到会话"""
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {'messages': [], 'created_at': datetime.now().isoformat()}
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        self.active_sessions[session_id]['messages'].append(message)
        self.active_sessions[session_id]['last_activity'] = datetime.now().isoformat()
        self.save_session(session_id)
    
    def save_session(self, session_id: str):
        """保存会话到文件"""
        try:
            session_file = self.conversations_path / f"{session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.active_sessions[session_id], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话失败: {e}")
    
    def load_session(self, session_id: str) -> Dict:
        """加载会话"""
        try:
            session_file = self.conversations_path / f"{session_id}.json"
            if session_file.exists():
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                self.active_sessions[session_id] = session_data
                return session_data
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
        return None
    
    def get_session_list(self) -> List[str]:
        """获取会话列表"""
        try:
            sessions = []
            for file in self.conversations_path.glob("session_*.json"):
                sessions.append(file.stem)
            return sorted(sessions, reverse=True)
        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return []

# 全局会话管理器
session_manager = SessionManager()

# 智能响应生成器
class ResponseGenerator:
    def __init__(self):
        self.response_queue = queue.Queue()
    
    def generate_response(self, query: str, session_id: str) -> str:
        """生成智能回答 - 集成RAG + MCP + DeepSeek"""
        if not components.initialized:
            raise RuntimeError("❌ 系统组件未初始化")
        
        start_time = time.time()
        logger.info(f"🧠 开始处理查询: {query}")
        
        # 1. RAG检索 - 必须成功
        try:
            rag_results = components.rag_retriever.search(query, top_k=3)
            logger.info(f"✅ RAG检索完成，找到 {len(rag_results)} 个结果")
        except Exception as e:
            raise RuntimeError(f"❌ RAG检索失败: {e}")
        
        # 2. MCP智能搜索 - 必须成功
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            mcp_results = loop.run_until_complete(
                components.mcp_engine.intelligent_search(query, max_results=3)
            )
            logger.info(f"✅ MCP搜索完成，找到 {len(mcp_results)} 个结果")
        except Exception as e:
            raise RuntimeError(f"❌ MCP搜索失败: {e}")
        
        # 3. 构建上下文
        context_parts = []
        
        # 添加MCP结果
        if mcp_results:
            context_parts.append("【智能检索结果】")
            for i, result in enumerate(mcp_results, 1):
                title = result.title if hasattr(result, 'title') else result.get('title', '未知')
                content = result.content if hasattr(result, 'content') else result.get('content', '')
                score = result.score if hasattr(result, 'score') else result.get('score', 0)
                context_parts.append(f"{i}. {title} (相关度: {score:.2f})\n   {content[:200]}...")
        
        # 添加RAG结果
        if rag_results:
            context_parts.append("\n【文档检索结果】")
            for i, result in enumerate(rag_results, 1):
                content = result.get('content', '')[:200]
                score = result.get('combined_score', result.get('score', 0))
                context_parts.append(f"{i}. 文档片段 (相关度: {score:.2f})\n   {content}...")
        
        context = "\n\n".join(context_parts)
        
        # 4. DeepSeek生成回答 - 必须成功
        try:
            response = components.deepseek_api.generate_response(query, context)
            if not response or "生成回答时发生错误" in response:
                raise RuntimeError("DeepSeek模型响应异常")
            
            elapsed_time = time.time() - start_time
            logger.info(f"✅ 回答生成完成，耗时: {elapsed_time:.2f}秒")
            
            # 检查是否超过30秒要求
            if elapsed_time > 30:
                logger.warning(f"⚠️ 响应时间超过30秒: {elapsed_time:.2f}秒")
            
            return response
            
        except Exception as e:
            raise RuntimeError(f"❌ DeepSeek生成失败: {e}")

# 全局响应生成器
response_generator = ResponseGenerator()

# ==================== API 端点实现 ====================

@app.route('/api/health', methods=['GET'])
def health_check():
    """系统健康检查"""
    try:
        health_status = {
            'status': 'healthy' if components.initialized else 'initializing',
            'timestamp': datetime.now().isoformat(),
            'components': {
                'rag_retriever': components.rag_retriever is not None,
                'deepseek_api': components.deepseek_api is not None and components.deepseek_api.available,
                'mcp_engine': components.mcp_engine is not None,
                'voice_engine': components.voice_engine is not None
            },
            'vector_db_chunks': len(components.rag_retriever.chunks) if components.rag_retriever else 0,
            'service': '家庭私人医生小帮手',
            'version': '2.0.0-vue-flask'
        }

        return jsonify(health_status), 200
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat_endpoint():
    """聊天接口 - 处理用户查询和AI响应"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400

        query = data.get('message', '').strip()
        session_id = data.get('session_id')

        if not query:
            return jsonify({'error': '查询内容不能为空'}), 400

        # 创建或验证会话
        if not session_id:
            session_id = session_manager.create_session()

        # 添加用户消息
        session_manager.add_message(session_id, 'user', query)

        # 生成AI回答
        start_time = time.time()
        try:
            response = response_generator.generate_response(query, session_id)
            response_time = time.time() - start_time

            # 添加AI回答到会话
            session_manager.add_message(session_id, 'assistant', response, {
                'response_time': response_time,
                'query_length': len(query),
                'response_length': len(response)
            })

            return jsonify({
                'response': response,
                'session_id': session_id,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }), 200

        except Exception as e:
            error_msg = f"生成回答失败: {str(e)}"
            logger.error(error_msg)

            # 记录错误到会话
            session_manager.add_message(session_id, 'system', f"错误: {error_msg}")

            return jsonify({
                'error': error_msg,
                'session_id': session_id,
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"聊天接口错误: {e}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/chat/stream', methods=['POST'])
def chat_stream_endpoint():
    """流式聊天接口 - 支持SSE实时响应"""
    try:
        data = request.get_json()
        query = data.get('message', '').strip()
        session_id = data.get('session_id')

        if not query:
            return jsonify({'error': '查询内容不能为空'}), 400

        if not session_id:
            session_id = session_manager.create_session()

        def generate_stream():
            try:
                # 发送开始信号
                yield f"data: {json.dumps({'type': 'start', 'session_id': session_id})}\n\n"

                # 添加用户消息
                session_manager.add_message(session_id, 'user', query)
                yield f"data: {json.dumps({'type': 'user_message', 'content': query})}\n\n"

                # 发送处理状态
                yield f"data: {json.dumps({'type': 'processing', 'message': '正在检索相关资料...'})}\n\n"

                # 生成回答
                response = response_generator.generate_response(query, session_id)

                # 发送AI回答
                session_manager.add_message(session_id, 'assistant', response)
                yield f"data: {json.dumps({'type': 'response', 'content': response})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'complete', 'timestamp': datetime.now().isoformat()})}\n\n"

            except Exception as e:
                error_msg = f"流式响应错误: {str(e)}"
                logger.error(error_msg)
                yield f"data: {json.dumps({'type': 'error', 'message': error_msg})}\n\n"

        return Response(
            stream_with_context(generate_stream()),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )

    except Exception as e:
        logger.error(f"流式聊天接口错误: {e}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/sessions', methods=['GET'])
def get_sessions():
    """获取会话列表"""
    try:
        sessions = session_manager.get_session_list()
        session_details = []

        for session_id in sessions[:20]:  # 限制返回最近20个会话
            session_data = session_manager.load_session(session_id)
            if session_data:
                session_details.append({
                    'session_id': session_id,
                    'created_at': session_data.get('created_at'),
                    'last_activity': session_data.get('last_activity'),
                    'message_count': len(session_data.get('messages', [])),
                    'preview': session_data.get('messages', [{}])[-1].get('content', '')[:100] if session_data.get('messages') else ''
                })

        return jsonify({'sessions': session_details}), 200
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        return jsonify({'error': f'获取会话列表失败: {str(e)}'}), 500

@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """获取特定会话详情"""
    try:
        session_data = session_manager.load_session(session_id)
        if not session_data:
            return jsonify({'error': '会话不存在'}), 404

        return jsonify({
            'session_id': session_id,
            'session_data': session_data
        }), 200
    except Exception as e:
        logger.error(f"获取会话详情失败: {e}")
        return jsonify({'error': f'获取会话详情失败: {str(e)}'}), 500

@app.route('/api/sessions', methods=['POST'])
def create_session():
    """创建新会话"""
    try:
        session_id = session_manager.create_session()
        return jsonify({
            'session_id': session_id,
            'created_at': datetime.now().isoformat()
        }), 201
    except Exception as e:
        logger.error(f"创建会话失败: {e}")
        return jsonify({'error': f'创建会话失败: {str(e)}'}), 500

@app.route('/api/sessions/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    """删除会话"""
    try:
        session_file = Path(CONFIG['CONVERSATION_PATH']) / f"{session_id}.json"
        if session_file.exists():
            session_file.unlink()
            if session_id in session_manager.active_sessions:
                del session_manager.active_sessions[session_id]
            return jsonify({'message': '会话删除成功'}), 200
        else:
            return jsonify({'error': '会话不存在'}), 404
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        return jsonify({'error': f'删除会话失败: {str(e)}'}), 500

@app.route('/api/voice/speak', methods=['POST'])
def voice_speak():
    """文本转语音"""
    try:
        if not components.voice_engine:
            return jsonify({'error': '语音功能不可用'}), 503

        data = request.get_json()
        text = data.get('text', '').strip()

        if not text:
            return jsonify({'error': '文本内容不能为空'}), 400

        # 清理文本
        import re
        clean_text = re.sub(r'[#*`\[\]()]', '', text)
        clean_text = re.sub(r'https?://\S+', '', clean_text)
        clean_text = re.sub(r'[🏥🔍📋💊⚠️📚🎤🔊]', '', clean_text)
        clean_text = clean_text.replace('\n', ' ').strip()

        # 限制长度
        if len(clean_text) > 300:
            clean_text = clean_text[:300] + "..."

        # 异步播放语音
        def speak_async():
            try:
                components.voice_engine.say(clean_text)
                components.voice_engine.runAndWait()
            except Exception as e:
                logger.error(f"语音播放失败: {e}")

        thread = threading.Thread(target=speak_async)
        thread.daemon = True
        thread.start()

        return jsonify({
            'message': '语音播放已开始',
            'text': clean_text,
            'length': len(clean_text)
        }), 200

    except Exception as e:
        logger.error(f"语音播放失败: {e}")
        return jsonify({'error': f'语音播放失败: {str(e)}'}), 500

@app.route('/api/voice/recognize', methods=['POST'])
def voice_recognize():
    """语音识别"""
    try:
        if not components.voice_recognizer or not components.microphone:
            return jsonify({'error': '语音识别功能不可用'}), 503

        data = request.get_json()
        timeout = data.get('timeout', 10)
        phrase_time_limit = data.get('phrase_time_limit', 15)

        def recognize_speech():
            try:
                with components.microphone as source:
                    logger.info("🎤 开始监听语音...")
                    audio = components.voice_recognizer.listen(
                        source,
                        timeout=timeout,
                        phrase_time_limit=phrase_time_limit
                    )

                logger.info("🔄 正在识别语音...")

                # 尝试多种识别方式
                try:
                    text = components.voice_recognizer.recognize_google(audio, language='zh-CN')
                    return text
                except:
                    try:
                        text = components.voice_recognizer.recognize_sphinx(audio, language='zh-CN')
                        return text
                    except:
                        return None

            except sr.WaitTimeoutError:
                return "TIMEOUT"
            except sr.UnknownValueError:
                return "UNKNOWN"
            except Exception as e:
                logger.error(f"语音识别异常: {e}")
                return None

        # 在后台线程中执行语音识别
        result_queue = queue.Queue()

        def recognition_thread():
            result = recognize_speech()
            result_queue.put(result)

        thread = threading.Thread(target=recognition_thread)
        thread.daemon = True
        thread.start()
        thread.join(timeout + 5)  # 等待识别完成

        try:
            result = result_queue.get_nowait()
            if result == "TIMEOUT":
                return jsonify({'error': '语音输入超时'}), 408
            elif result == "UNKNOWN":
                return jsonify({'error': '无法识别语音内容'}), 400
            elif result:
                return jsonify({
                    'text': result,
                    'confidence': 0.8,  # 模拟置信度
                    'timestamp': datetime.now().isoformat()
                }), 200
            else:
                return jsonify({'error': '语音识别失败'}), 500
        except queue.Empty:
            return jsonify({'error': '语音识别超时'}), 408

    except Exception as e:
        logger.error(f"语音识别失败: {e}")
        return jsonify({'error': f'语音识别失败: {str(e)}'}), 500

@app.route('/api/documents/upload', methods=['POST'])
def upload_documents():
    """文档上传和处理"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'error': '没有选择文件'}), 400

        upload_path = Path(CONFIG['UPLOAD_PATH'])
        upload_path.mkdir(exist_ok=True)

        results = {
            'total_files': len(files),
            'successful_uploads': 0,
            'failed_uploads': 0,
            'processed_chunks': 0,
            'errors': [],
            'file_details': []
        }

        supported_formats = ['.pdf', '.txt', '.docx', '.doc']

        for file in files:
            try:
                # 检查文件格式
                file_ext = Path(file.filename).suffix.lower()
                if file_ext not in supported_formats:
                    results['failed_uploads'] += 1
                    results['errors'].append(f"{file.filename}: 不支持的文件格式")
                    continue

                # 检查文件大小
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(0)  # 重置到文件开头

                if file_size > CONFIG['MAX_FILE_SIZE']:
                    results['failed_uploads'] += 1
                    results['errors'].append(f"{file.filename}: 文件过大")
                    continue

                # 保存文件
                file_path = upload_path / file.filename
                file.save(str(file_path))

                # 处理文件（这里简化处理，实际应该集成到向量数据库）
                results['successful_uploads'] += 1
                results['processed_chunks'] += 10  # 模拟处理的块数

                file_detail = {
                    'name': file.filename,
                    'size': file_size,
                    'chunks': 10,  # 模拟
                    'upload_time': datetime.now().isoformat(),
                    'file_type': file_ext
                }
                results['file_details'].append(file_detail)

            except Exception as e:
                results['failed_uploads'] += 1
                results['errors'].append(f"{file.filename}: {str(e)}")

        return jsonify(results), 200

    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        return jsonify({'error': f'文档上传失败: {str(e)}'}), 500

@app.route('/api/documents', methods=['GET'])
def get_documents():
    """获取已上传的文档列表"""
    try:
        upload_path = Path(CONFIG['UPLOAD_PATH'])
        documents_path = Path(CONFIG['DOCUMENTS_PATH'])

        documents = []

        # 扫描上传目录
        for file_path in upload_path.glob('*'):
            if file_path.is_file():
                documents.append({
                    'name': file_path.name,
                    'size': file_path.stat().st_size,
                    'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                    'type': 'uploaded',
                    'path': str(file_path.relative_to(Path.cwd()))
                })

        # 扫描文档目录
        for file_path in documents_path.glob('*'):
            if file_path.is_file():
                documents.append({
                    'name': file_path.name,
                    'size': file_path.stat().st_size,
                    'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                    'type': 'system',
                    'path': str(file_path.relative_to(Path.cwd()))
                })

        return jsonify({
            'documents': documents,
            'total_count': len(documents),
            'vector_db_chunks': len(components.rag_retriever.chunks) if components.rag_retriever else 0
        }), 200

    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return jsonify({'error': f'获取文档列表失败: {str(e)}'}), 500

# ==================== 系统初始化和启动 ====================

def initialize_system():
    """初始化系统 - 严格要求100%成功"""
    try:
        logger.info("🚀 开始初始化家庭私人医生小帮手系统...")

        # 创建必要目录
        for path_key in ['DOCUMENTS_PATH', 'CONVERSATION_PATH', 'UPLOAD_PATH', 'VECTOR_DB_PATH']:
            Path(CONFIG[path_key]).mkdir(parents=True, exist_ok=True)

        # 初始化所有组件
        components.initialize()

        logger.info("🎉 系统初始化完成！")
        logger.info(f"📊 向量数据库: {len(components.rag_retriever.chunks)} 个文档块")
        logger.info(f"🤖 AI模型: {components.deepseek_api.model_name}")
        logger.info("🏥 家庭私人医生小帮手已就绪")

        return True

    except Exception as e:
        logger.error(f"❌ 系统初始化失败: {e}")
        raise

if __name__ == '__main__':
    try:
        # 初始化系统
        initialize_system()

        # 启动Flask服务器
        logger.info("🌐 启动Flask API服务器...")
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )

    except KeyboardInterrupt:
        logger.info("🛑 用户中断服务")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        exit(1)

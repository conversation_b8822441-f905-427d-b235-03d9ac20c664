#!/usr/bin/env python3
"""
🚀 完美统一中医智能助手 - 简化启动器
一键启动所有服务，确保系统完美运行
"""

import subprocess
import sys
import time
import webbrowser
import requests
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("📦 检查系统依赖...")
    
    required_packages = [
        ('streamlit', 'streamlit'),
        ('requests', 'requests'),
        ('faiss', 'faiss'),
        ('sentence_transformers', 'sentence_transformers'),
        ('PyPDF2', 'PyPDF2'),
        ('docx', 'python-docx'),
        ('pyttsx3', 'pyttsx3'),
        ('speech_recognition', 'SpeechRecognition')
    ]
    
    missing = []
    for module, package in required_packages:
        try:
            __import__(module)
            print(f"   ✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"   ❌ {package}")
    
    if missing:
        print(f"\n❌ 缺少依赖: {', '.join(missing)}")
        print("请运行: pip install -r requirements_perfect.txt")
        return False
    
    return True

def check_files():
    """检查核心文件"""
    print("📁 检查核心文件...")
    
    required_files = [
        'perfect_unified_tcm_system.py',
        'intelligent_rag_retriever.py',
        'intelligent_mcp_service.py',
        'deepseek_ollama_api.py'
    ]
    
    missing = []
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            missing.append(file)
            print(f"   ❌ {file}")
    
    if missing:
        print(f"\n❌ 缺少文件: {', '.join(missing)}")
        return False
    
    return True

def create_directories():
    """创建必要目录"""
    print("📁 创建必要目录...")
    
    directories = [
        'documents', 'perfect_vector_db', 'conversations',
        'uploads', 'models', 'logs', 'cache'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")

def start_mcp_service():
    """启动MCP服务"""
    print("🔍 启动智能MCP服务...")
    
    try:
        # 检查服务是否已运行
        response = requests.get('http://localhost:8006/health', timeout=3)
        if response.status_code == 200:
            print("✅ MCP服务已运行")
            return True
    except:
        pass
    
    try:
        process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        
        # 等待服务启动
        for i in range(10):
            time.sleep(1)
            try:
                response = requests.get('http://localhost:8006/health', timeout=2)
                if response.status_code == 200:
                    print("✅ MCP服务启动成功")
                    return True
            except:
                continue
        
        print("⚠️ MCP服务启动超时")
        return False
        
    except Exception as e:
        print(f"❌ MCP服务启动失败: {e}")
        return False

def test_system():
    """测试系统功能"""
    print("\n🧪 测试系统功能...")
    
    # 测试MCP服务
    try:
        response = requests.get('http://localhost:8006/health', timeout=5)
        if response.status_code == 200:
            print("   ✅ MCP服务正常")
        else:
            print("   ❌ MCP服务异常")
    except:
        print("   ❌ MCP服务不可用")
    
    # 测试向量数据库
    try:
        vector_db_path = Path('./perfect_vector_db')
        if vector_db_path.exists() and list(vector_db_path.glob('*.pkl')):
            print("   ✅ 向量数据库存在")
        else:
            print("   ⚠️ 向量数据库为空")
    except:
        print("   ❌ 向量数据库检查失败")
    
    # 测试模型
    try:
        model_path = Path('./models/m3e-base')
        if model_path.exists():
            print("   ✅ m3e-base模型存在")
        else:
            print("   ⚠️ m3e-base模型缺失")
    except:
        print("   ❌ 模型检查失败")

def start_main_system():
    """启动主系统"""
    print("\n🌐 启动完美统一中医智能助手...")
    print("💡 系统将在浏览器中自动打开")
    print("🔗 访问地址: http://localhost:8501")
    print("\n🎯 功能特色:")
    print("   ✅ 语音对话功能")
    print("   ✅ 智能RAG检索")
    print("   ✅ DeepSeek AI推理")
    print("   ✅ 聊天历史管理")
    print("   ✅ 文档上传处理")
    print("   ✅ 多端访问支持")
    print("\n🧪 测试建议:")
    print("   1. 输入: 失眠多梦怎么办")
    print("   2. 输入: 肾虚怎么治疗")
    print("   3. 尝试语音输入功能")
    print("   4. 上传PDF文档测试")
    print()
    
    try:
        # 自动打开浏览器
        time.sleep(2)
        webbrowser.open('http://localhost:8501')
        
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "perfect_unified_tcm_system.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🏥 完美统一中医智能助手 - 简化启动器")
    print("🎯 一键启动所有服务")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return False
    
    # 检查文件
    if not check_files():
        input("按回车键退出...")
        return False
    
    # 创建目录
    create_directories()
    
    # 启动MCP服务
    start_mcp_service()
    
    # 测试系统
    test_system()
    
    # 启动主系统
    return start_main_system()

if __name__ == "__main__":
    print("📋 使用说明:")
    print("1. 此启动器会自动检查依赖和启动所有服务")
    print("2. 然后启动完美统一中医智能助手")
    print("3. 支持语音对话、智能检索、文档上传等功能")
    print("4. 按 Ctrl+C 停止系统")
    print()
    
    success = main()
    
    if not success:
        print("\n❌ 启动失败")
        print("💡 故障排除:")
        print("1. 检查Python环境和依赖")
        print("2. 确保端口8501和8006未被占用")
        print("3. 检查文件权限")
        print("4. 查看错误日志")
        input("按回车键退出...")
    
    sys.exit(0 if success else 1)
